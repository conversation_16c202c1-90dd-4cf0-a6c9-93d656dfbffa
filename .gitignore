# Rust
/target/
**/*.rs.bk
*.pdb

# Cargo
Cargo.lock

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Telemetry scan results
telemetry_scan_*.json

# Temporary files
*.tmp
*.temp

# Build artifacts
*.exe
*.dll
*.so
*.dylib

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Local configuration
config.local.*
.local

# Test artifacts
test-results/
coverage/

# Backup files
*.bak
*.backup
*~

# Editor directories and files
.vscode/
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Rust-specific
**/*.rs.bk
*.orig

# Profiling
*.prof

# Memory dumps
*.hprof
*.dump

# JetBrains
.idea/
*.iml
*.ipr
*.iws

# Visual Studio
.vs/
*.user
*.userosscache
*.sln.docstates
*.userprefs

# Windows
*.lnk

# macOS
.AppleDouble
.LSOverride
Icon

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Project-specific
# Ignore generated scan results
scan_results/
output/
reports/

# Ignore sensitive configuration
secrets.toml
private.key
*.pem

# Ignore large test files
test_data/large/
*.large

# Ignore compiled documentation
doc/target/
