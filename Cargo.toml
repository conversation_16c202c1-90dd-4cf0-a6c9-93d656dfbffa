[package]
name = "telemetry_watcher"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
notify = "6.0"
walkdir = "2.3"
regex = "1.7"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
clap = { version = "4.0", features = ["derive"] }
colored = "2.0"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4"] }
machine-uid = "0.3"
sysinfo = "0.30"
sha2 = "0.10"
mac_address = "1.0"
