# Privacy & Security Documentation

## Overview

This telemetry scanner implements privacy-respecting machine fingerprinting and analysis capabilities. This document explains exactly which components are collected, how they are processed, and what privacy protections are in place.

## Machine Fingerprinting

### What We Collect

The scanner generates a deterministic machine fingerprint using the following hardware components:

1. **CPU Information**
   - CPU brand/model string (e.g., "Intel Core i7-9700K")
   - Used for: Hardware identification
   - Privacy: Non-sensitive, publicly available information

2. **System UUID** 
   - Motherboard/BIOS UUID when available
   - Used for: Primary hardware identification
   - Privacy: Hashed before storage, never transmitted raw

3. **Primary Disk Identifier**
   - Serial number or identifier of the first non-removable disk
   - Used for: Hardware uniqueness
   - Privacy: Hashed before storage, never transmitted raw

4. **MAC Address**
   - MAC address of the primary network interface
   - Used for: Network hardware identification
   - Privacy: **CRITICAL** - Hashed before storage, flagged if transmitted raw

5. **System Memory Size**
   - Total system RAM in bytes
   - Used for: Additional entropy in fingerprint
   - Privacy: Low-sensitivity system specification

### Privacy Protections

#### 1. Cryptographic Hashing
- All hardware identifiers are combined and hashed using SHA-256
- Salt: `"telemetry-scanner-v1"` (rotated when algorithm changes)
- Result: 64-character hex string that cannot be reversed to original values
- **Raw hardware data never leaves the host machine**

#### 2. Deterministic but Non-Reversible
- Same hardware → same fingerprint (for consistency)
- Fingerprint → cannot determine original hardware IDs
- Collision probability: < 2^-128 for real-world fleet sizes

#### 3. Entropy Analysis
- Fingerprints with insufficient entropy (< 2.0 bits) are flagged
- Requires minimum 3 hardware components for validity
- Low-entropy fingerprints indicate potential VM/cloud environments

## Privacy Risk Assessment

### Risk Levels

#### CRITICAL
- Raw MAC addresses being transmitted
- Unencrypted hardware serial numbers sent to external servers
- Direct hardware identifiers in network requests

#### HIGH  
- Machine-specific UUIDs with high entropy
- Hardware identifiers that could uniquely identify a device
- Telemetry data containing raw system information

#### MEDIUM
- Possible machine identifiers or tracking tokens
- System information that could contribute to fingerprinting
- Encoded but not properly hashed identifiers

#### LOW
- Properly hashed identifiers
- Low-entropy values (likely test data)
- System specifications that are not uniquely identifying

### Detection Categories

#### Raw vs Hashed IDs
- **Raw IDs**: Direct hardware identifiers (HIGH RISK)
- **Hashed IDs**: Cryptographically processed identifiers (LOW RISK)
- Scanner automatically detects hash patterns (SHA-256, SHA-1, MD5, Base64)

#### Data Types Detected
- `mac_address`: Network hardware identifiers
- `cpu_info`: Processor information
- `disk_serial`: Storage device identifiers  
- `uuid`: Universal unique identifiers
- `machine_identifier`: General hardware IDs
- `telemetry_data`: Analytics and usage data
- `system_information`: OS and hardware specs
- `network_data`: Network configuration data

## Compliance & Best Practices

### Recommended Practices

1. **Always Hash Hardware IDs**
   ```rust
   // Good: Hashed identifier
   let fingerprint = sha256(hardware_id + salt);
   
   // Bad: Raw hardware ID
   let raw_mac = get_mac_address();
   ```

2. **Use Proper Salt/Key Management**
   - Rotate salts when algorithm changes
   - Use per-tenant keys for server-side verification
   - Never hardcode salts in client code

3. **Minimize Data Collection**
   - Only collect identifiers necessary for your use case
   - Prefer derived/computed values over raw hardware data
   - Implement data retention policies

4. **Transparency & Consent**
   - Document exactly what hardware data is collected
   - Provide opt-out mechanisms where legally required
   - Clearly explain fingerprinting purposes

### Virtual Machine Considerations

Cloud and VM environments often share hardware identifiers:
- BIOS UUIDs may be identical across instances
- MAC addresses often use shared OUI blocks
- Disk serials may be virtualized

**Mitigation**: Append cloud-provided instance IDs or use TPM-based attestation where available.

### Hardware Replacement Scenarios

When users upgrade hardware, fingerprints will change:
- Maintain secondary UUID (random v4) as fallback identifier
- Store `(fingerprint, uuid, first_seen)` tuples
- Implement fingerprint migration logic for known users

## Scanner Configuration

### CLI Options

- `--show-fingerprint`: Display local machine fingerprint for debugging
- `--privacy-analysis`: Enable detailed privacy risk analysis
- `--penetration-level extreme`: Maximum detection sensitivity

### Privacy Analysis Output

The scanner provides detailed privacy metrics:
- Critical/High/Medium/Low risk counts
- Raw vs hashed identifier detection
- Potential fingerprint transmission events
- Entropy scores for uniqueness assessment

## Legal & Regulatory Notes

### GDPR Compliance
- Machine fingerprints may constitute personal data under GDPR
- Implement lawful basis for processing (consent, legitimate interest)
- Provide data subject rights (access, deletion, portability)
- Document processing purposes and retention periods

### Other Jurisdictions
- California Consumer Privacy Act (CCPA): Similar requirements to GDPR
- Sector-specific regulations may apply (HIPAA, PCI-DSS, etc.)
- Consult legal counsel for jurisdiction-specific requirements

## Technical Implementation

### Fingerprint Generation Algorithm

```rust
fn generate_fingerprint() -> String {
    let components = [
        format!("cpu:{}", get_cpu_brand()),
        format!("bios:{}", get_system_uuid()),
        format!("disk:{}", get_disk_serial()),
        format!("mac:{}", get_mac_address()),
        format!("mem:{}", get_memory_size()),
    ];
    
    components.sort(); // Deterministic ordering
    let joined = components.join("|");
    let salt = "telemetry-scanner-v1";
    
    sha256(salt + joined) // 64-char hex output
}
```

### Entropy Calculation

Shannon entropy is calculated across all fingerprint components:
```
H(X) = -Σ p(x) * log₂(p(x))
```

Where p(x) is the probability of each character in the combined component string.

### Hash Detection Patterns

The scanner recognizes these hash formats:
- SHA-256: 64 hex characters
- SHA-1: 40 hex characters  
- MD5: 32 hex characters
- Base64: Standard Base64 encoding patterns

## Contact & Support

For questions about privacy implementation or compliance:
- Review this documentation thoroughly
- Test with `--show-fingerprint` and `--privacy-analysis` flags
- Implement proper hashing before any network transmission
- Consider legal review for production deployments

**Remember**: The goal is to enable legitimate use cases while protecting user privacy through proper cryptographic techniques and transparent practices.
