# Telemetry & Machine ID Watcher

A comprehensive Rust-based security scanner that detects telemetry collection and machine identification code across your file system.

## Features

- 🔍 **Deep Pattern Matching**: Detects various telemetry and machine ID patterns
- 👁️ **Real-time File Watching**: Monitors file changes and scans new/modified files
- 🎯 **Multi-language Support**: Scans Rust, Python, JavaScript, C++, C#, Java, Go files
- 📊 **Detailed Reporting**: Categorizes findings by severity and type
- 💾 **JSON Export**: Save results for further analysis
- 🎨 **Colored Output**: Easy-to-read terminal output with color coding

## Detection Categories

### Machine ID & Device Identification
- Machine/Device/Hardware ID generation
- UUID generation for identification
- MAC address collection
- CPU information and serial numbers
- Disk/Drive serial number collection
- Windows Registry access
- WMI queries for system information

### Telemetry & Analytics
- Telemetry/Analytics collection
- Data transmission to external servers
- User behavior tracking
- Crash/Error reporting
- Network requests and API calls
- External domain references

### Rust-Specific Patterns
- `sysinfo` crate usage
- `machine-uid` crate usage
- `reqwest` HTTP client usage

## Installation

```bash
# Clone or create the project
cargo build --release
```

## Usage

### Basic Scan
```bash
# Scan C:\Users directory (default)
cargo run

# Scan specific directory
cargo run -- --path "C:\Program Files"

# Scan with custom file extensions
cargo run -- --extensions "rs,py,js" --path "/home/<USER>"
```

### File Watching Mode
```bash
# Enable real-time file watching
cargo run -- --watch --path "C:\Users"
```

### Export Results
```bash
# Save results to JSON file
cargo run -- --output results.json --path "C:\Users"
```

### Full Example
```bash
# Comprehensive scan with all options
cargo run -- --path "C:\Users" --watch --output scan_results.json --extensions "rs,py,js,ts,cpp,c,h,cs,java,go"
```

## Command Line Options

- `-p, --path <PATH>`: Directory to scan (default: C:\Users)
- `-w, --watch`: Enable real-time file watching
- `-o, --output <FILE>`: Save results to JSON file
- `-e, --extensions <EXTS>`: File extensions to scan (comma-separated)

## Output Format

The scanner provides:
- **Severity Levels**: HIGH, MEDIUM, LOW
- **Categories**: MACHINE_ID, TELEMETRY, SYSTEM_INFO, NETWORK, FILESYSTEM, RUST_SPECIFIC
- **File Location**: Exact file path and line number
- **Code Context**: The actual code line that triggered the detection

## Example Output

```
🔍 Telemetry & Machine ID Watcher
Scanning path: C:\Users
Extensions: rs, py, js, ts, cpp, c, h, hpp, cs, java, go

📊 Starting initial scan...
🔍 Scanning directory: C:\Users
📄 Scanned 100 files, 5 detections so far

📋 SCAN RESULTS
Files scanned: 1,234
Directories scanned: 567
Detections found: 23

🚨 DETECTIONS:

Machine/Device ID generation (MACHINE_ID) (8)
  HIGH C:\Users\<USER>\project\src\main.rs:45
    let machine_id = machine_uid::get().unwrap();
  HIGH C:\Users\<USER>\app\telemetry.py:12
    device_id = uuid.uuid4()

Telemetry/Analytics collection (TELEMETRY) (15)
  HIGH C:\Users\<USER>\analytics\tracker.js:78
    analytics.track('user_action', data);
  MEDIUM C:\Users\<USER>\src\crash_reporter.cpp:23
    send_crash_report(exception_data);
```

## Security Considerations

This tool is designed to help identify potentially privacy-invasive code patterns. Use it to:
- Audit your codebase for unintended telemetry
- Ensure compliance with privacy regulations
- Review third-party dependencies
- Monitor for unauthorized data collection

## Contributing

Feel free to add new patterns or improve existing ones by modifying `src/patterns.rs`.

## License

MIT License - Use responsibly and respect privacy laws.
