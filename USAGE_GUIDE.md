# Advanced Telemetry & Machine ID Watcher - Usage Guide

## 🚀 Quick Start

### Option 1: Test with Python (Immediate)
```bash
# Test the pattern detection immediately
python test_patterns.py examples
python test_patterns.py "C:\Users"
python test_patterns.py "C:\Program Files"
```

### Option 2: Advanced Rust Version (Recommended)
```bash
# Install Rust and build the advanced version
.\setup.ps1

# Or manually:
# 1. Install Rust from https://rustup.rs/
# 2. cargo build --release
# 3. cargo run -- --help
```

## 🔥 New Advanced Features

### ✅ **Auto-Save with Unique DateTime Names**
- Every scan automatically saves results with unique timestamps
- Format: `telemetry_scan_YYYYMMDD_HHMMSS_[UUID].json`
- No more manual output file management!

### ✅ **High-Penetration Scanning**
- **Deep Structure Analysis**: Detects parent-child relationships
- **Obfuscation Detection**: Identifies encoded/encrypted patterns
- **Data Flow Analysis**: Tracks data movement patterns
- **Complex ID Structure Recognition**: Finds nested identification systems

### ✅ **Multi-Level Penetration**
- **Low**: Basic patterns only
- **Medium**: Standard + behavioral patterns
- **High**: Advanced + obfuscated patterns (default)
- **Extreme**: All patterns + anti-analysis detection

## What It Detects

### 🔴 HIGH Severity
- **Machine ID Generation**: `machine_id`, `device_id`, `hardware_id`
- **MAC Address Collection**: Network interface access
- **CPU Information**: Processor serial numbers, CPU ID
- **Disk Serial Numbers**: Drive identification
- **Telemetry Collection**: Analytics, tracking, metrics
- **Data Transmission**: Sending data to external servers
- **Registry Access**: Windows registry queries
- **WMI Queries**: System information gathering

### 🟡 MEDIUM Severity  
- **UUID Generation**: For device identification
- **User Behavior Tracking**: Click tracking, session data
- **Crash Reporting**: Error reporting systems
- **Network Requests**: HTTP calls that might transmit data
- **System Information**: OS version, platform info

### 🟢 LOW Severity
- **External Domains**: References to tracking domains
- **Configuration Files**: Access to settings files

## Usage Examples

### Scan Specific Directories
```bash
# Scan user directories
cargo run -- --path "C:\Users"

# Scan program files
cargo run -- --path "C:\Program Files"

# Scan with custom extensions
cargo run -- --path "C:\Source" --extensions "rs,py,js"
```

### Real-time Monitoring
```bash
# Watch for file changes and scan new/modified files
cargo run -- --watch --path "C:\Development"
```

### Export Results
```bash
# Save detailed results to JSON
cargo run -- --path "C:\Users" --output scan_results.json
```

### Python Quick Test
```bash
# Quick test without building Rust
python test_patterns.py "C:\Users\<USER>\Documents"
python test_patterns.py single_file.rs
```

## Understanding the Output

### Detection Format
```
🚨 DETECTIONS:

MACHINE_ID (5)
  🔴 HIGH C:\path\to\file.rs:45
    Machine/Device ID generation or usage
    let machine_id = machine_uid::get().unwrap();
```

- **Category**: MACHINE_ID, TELEMETRY, SYSTEM_INFO, etc.
- **Severity**: 🔴 HIGH, 🟡 MEDIUM, 🟢 LOW  
- **Location**: File path and line number
- **Description**: What type of pattern was detected
- **Code**: The actual line that triggered the detection

### Categories Explained
- **MACHINE_ID**: Hardware/device identification
- **TELEMETRY**: Data collection and analytics
- **SYSTEM_INFO**: System information gathering
- **NETWORK**: Network requests and external communication
- **FILESYSTEM**: File system access patterns
- **RUST_SPECIFIC**: Rust crate usage patterns

## File Extensions Scanned
By default: `.rs`, `.py`, `.js`, `.ts`, `.cpp`, `.c`, `.h`, `.hpp`, `.cs`, `.java`, `.go`

## Performance Tips

1. **Start Small**: Test with a small directory first
2. **Use Extensions**: Limit to relevant file types with `--extensions`
3. **Watch Mode**: Use `--watch` for ongoing monitoring
4. **Export Results**: Use `--output` to save and analyze results later

## Common Use Cases

### Security Audit
```bash
# Comprehensive scan of user data
cargo run -- --path "C:\Users" --output security_audit.json
```

### Development Monitoring  
```bash
# Watch development directory for new telemetry code
cargo run -- --watch --path "C:\Development\MyProject"
```

### Third-party Analysis
```bash
# Scan installed programs
cargo run -- --path "C:\Program Files" --extensions "exe,dll,js,py"
```

### Privacy Compliance
```bash
# Regular scans for compliance
cargo run -- --path "C:\CompanyCode" --output compliance_$(date +%Y%m%d).json
```

## Troubleshooting

### Rust Not Installed
- Run `setup.ps1` or install from https://rustup.rs/
- Restart terminal after installation

### Permission Errors
- Run as Administrator for system directories
- Some directories may require elevated permissions

### Large Directories
- Use `--extensions` to limit file types
- Consider scanning subdirectories separately
- The tool shows progress every 100 files

### False Positives
- Review the actual code context provided
- Some patterns may be legitimate (e.g., crash reporting)
- Focus on HIGH severity items first

## Next Steps

After running the scanner:
1. **Review HIGH severity detections** first
2. **Analyze the code context** to determine if it's legitimate
3. **Check for data transmission** patterns
4. **Verify user consent** for any telemetry
5. **Document findings** for compliance purposes
