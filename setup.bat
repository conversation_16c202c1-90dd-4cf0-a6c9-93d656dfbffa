@echo off
echo Installing Rust and Cargo...
echo.

REM Download and install Rust
echo Downloading Rust installer...
powershell -Command "Invoke-WebRequest -Uri 'https://win.rustup.rs/x86_64' -OutFile 'rustup-init.exe'"

echo Running Rust installer...
rustup-init.exe -y

REM Add Cargo to PATH for current session
set PATH=%PATH%;%USERPROFILE%\.cargo\bin

echo.
echo Rust installation complete!
echo.
echo Building the telemetry watcher...
cargo build --release

echo.
echo Setup complete! You can now run:
echo   cargo run -- --help
echo   cargo run -- --path "C:\Users"
echo   cargo run -- --watch --path "C:\Users"
echo.
pause
