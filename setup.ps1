# PowerShell script to set up the Advanced Telemetry Watcher

Write-Host "🦀 Setting up Advanced Rust Telemetry & Machine ID Watcher" -ForegroundColor Cyan
Write-Host "🔍 High-Penetration Scanner with Auto-Save & Complex Structure Detection" -ForegroundColor Yellow
Write-Host "=" * 70

# Check if Rust is already installed
$rustInstalled = Get-Command cargo -ErrorAction SilentlyContinue
if ($rustInstalled) {
    Write-Host "✅ Rust is already installed" -ForegroundColor Green
} else {
    Write-Host "📥 Installing Rust..." -ForegroundColor Yellow
    
    # Download and install Rust
    try {
        Invoke-WebRequest -Uri "https://win.rustup.rs/x86_64" -OutFile "rustup-init.exe"
        .\rustup-init.exe -y
        
        # Add Cargo to PATH for current session
        $env:PATH += ";$env:USERPROFILE\.cargo\bin"
        
        Write-Host "✅ Rust installed successfully!" -ForegroundColor Green
        
        # Clean up installer
        Remove-Item "rustup-init.exe" -ErrorAction SilentlyContinue
    }
    catch {
        Write-Host "❌ Failed to install Rust: $_" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "🔨 Building the telemetry watcher..." -ForegroundColor Yellow

try {
    cargo build --release
    Write-Host "✅ Build successful!" -ForegroundColor Green
}
catch {
    Write-Host "❌ Build failed: $_" -ForegroundColor Red
    Write-Host "Try running: cargo build --release" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "🎉 Setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 Advanced Usage Examples:" -ForegroundColor Cyan
Write-Host "  cargo run -- --help" -ForegroundColor White
Write-Host "  cargo run -- --path `"C:\Users`" --deep-scan" -ForegroundColor White
Write-Host "  cargo run -- --watch --path `"C:\Users`" --penetration-level high" -ForegroundColor White
Write-Host "  cargo run -- --path `"C:\Program Files`" --analyze-structures" -ForegroundColor White
Write-Host ""
Write-Host "📊 Features:" -ForegroundColor Green
Write-Host "  ✅ Auto-saves all results with unique datetime stamps" -ForegroundColor White
Write-Host "  ✅ Deep penetration scanning for complex ID structures" -ForegroundColor White
Write-Host "  ✅ Parent-child relationship analysis" -ForegroundColor White
Write-Host "  ✅ Advanced pattern recognition for obfuscated code" -ForegroundColor White
Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
