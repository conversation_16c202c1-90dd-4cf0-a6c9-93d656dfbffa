use sha2::{Digest, Sha256};
use sysinfo::System;
use std::collections::HashMap;
use serde::{Serialize, Deserialize};

/// Privacy-respecting machine fingerprint generator
/// 
/// This module creates deterministic, non-reversible machine fingerprints
/// by hashing hardware identifiers. Raw hardware data never leaves the host.
pub struct MachineFingerprint {
    fingerprint: String,
    components: Vec<String>,
    entropy_score: f64,
}

impl MachineFingerprint {
    /// Generate a deterministic machine fingerprint
    /// 
    /// This function:
    /// 1. Gathers hardware identifiers (CPU, BIOS, disk, MAC)
    /// 2. Normalizes and sorts them for consistency
    /// 3. Applies SHA-256 hashing with salt for privacy
    /// 4. Returns a 64-character hex string
    pub fn generate() -> Self {
        let mut parts: Vec<String> = Vec::new();
        let mut sys = System::new_all();
        sys.refresh_all();

        // 1. CPU information (brand, architecture)
        let cpu_brand = sys.global_cpu_info().brand();
        if !cpu_brand.is_empty() {
            parts.push(format!("cpu:{}", cpu_brand));
        }

        // 2. System UUID (motherboard/BIOS identifier)
        if let Some(system_uuid) = Self::get_system_uuid() {
            parts.push(format!("bios:{}", system_uuid));
        }

        // 3. First non-removable disk serial
        if let Some(disk_serial) = Self::get_primary_disk_serial(&sys) {
            parts.push(format!("disk:{}", disk_serial));
        }

        // 4. MAC address of primary network interface
        if let Some(mac_addr) = Self::get_primary_mac_address() {
            parts.push(format!("mac:{}", mac_addr));
        }

        // 5. System memory size (as additional entropy)
        let total_memory = sys.total_memory();
        if total_memory > 0 {
            parts.push(format!("mem:{}", total_memory));
        }

        // Calculate entropy before hashing
        let entropy_score = Self::calculate_entropy(&parts);

        // Normalize and sort for deterministic results
        parts.sort();
        let joined = parts.join("|");
        
        // Hash with salt for privacy
        let salt = "telemetry-scanner-v1";
        let mut hasher = Sha256::new();
        hasher.update(salt.as_bytes());
        hasher.update(joined.as_bytes());
        let fingerprint = format!("{:x}", hasher.finalize());

        Self {
            fingerprint,
            components: parts,
            entropy_score,
        }
    }

    /// Get the hashed fingerprint (safe to transmit)
    pub fn fingerprint(&self) -> &str {
        &self.fingerprint
    }

    /// Get component count (for debugging)
    pub fn component_count(&self) -> usize {
        self.components.len()
    }

    /// Get entropy score (higher = more unique)
    pub fn entropy_score(&self) -> f64 {
        self.entropy_score
    }

    /// Check if fingerprint has sufficient entropy for uniqueness
    pub fn has_sufficient_entropy(&self) -> bool {
        self.entropy_score > 2.0 && self.component_count() >= 3
    }

    /// Get component types (without raw values)
    pub fn component_types(&self) -> Vec<String> {
        self.components.iter()
            .map(|c| c.split(':').next().unwrap_or("unknown").to_string())
            .collect()
    }

    // Private helper methods

    fn get_system_uuid() -> Option<String> {
        // Try to get system UUID from various sources
        #[cfg(target_os = "windows")]
        {
            Self::get_windows_system_uuid()
        }
        #[cfg(target_os = "linux")]
        {
            Self::get_linux_system_uuid()
        }
        #[cfg(target_os = "macos")]
        {
            Self::get_macos_system_uuid()
        }
        #[cfg(not(any(target_os = "windows", target_os = "linux", target_os = "macos")))]
        {
            None
        }
    }

    #[cfg(target_os = "windows")]
    fn get_windows_system_uuid() -> Option<String> {
        // On Windows, try WMI query for system UUID
        // For now, return None - can be enhanced with winapi crate
        None
    }

    #[cfg(target_os = "linux")]
    fn get_linux_system_uuid() -> Option<String> {
        // Try reading from /sys/class/dmi/id/product_uuid
        std::fs::read_to_string("/sys/class/dmi/id/product_uuid")
            .ok()
            .map(|s| s.trim().to_string())
            .filter(|s| !s.is_empty() && s != "00000000-0000-0000-0000-000000000000")
    }

    #[cfg(target_os = "macos")]
    fn get_macos_system_uuid() -> Option<String> {
        // On macOS, could use IOKit to get hardware UUID
        // For now, return None - can be enhanced with system_profiler
        None
    }

    fn get_primary_disk_serial(_sys: &System) -> Option<String> {
        // Note: sysinfo 0.30 changed disk API, using placeholder for now
        // In production, would use platform-specific disk enumeration
        Some("disk_placeholder".to_string())
    }

    fn get_primary_mac_address() -> Option<String> {
        match mac_address::get_mac_address() {
            Ok(Some(mac)) => Some(mac.to_string()),
            _ => None,
        }
    }

    fn calculate_entropy(parts: &[String]) -> f64 {
        if parts.is_empty() {
            return 0.0;
        }

        let mut char_counts: HashMap<char, usize> = HashMap::new();
        let mut total_chars = 0;

        for part in parts {
            for ch in part.chars() {
                *char_counts.entry(ch).or_insert(0) += 1;
                total_chars += 1;
            }
        }

        if total_chars == 0 {
            return 0.0;
        }

        let mut entropy = 0.0;
        for &count in char_counts.values() {
            let probability = count as f64 / total_chars as f64;
            entropy -= probability * probability.log2();
        }

        entropy
    }
}

/// Analyze a string for potential machine ID patterns
pub fn analyze_id_string(value: &str) -> IdAnalysis {
    let mut analysis = IdAnalysis {
        is_likely_machine_id: false,
        entropy_score: 0.0,
        format_type: IdFormat::Unknown,
        privacy_risk: PrivacyRisk::Low,
        is_hashed: false,
    };

    // Calculate entropy
    analysis.entropy_score = calculate_string_entropy(value);

    // Detect format patterns
    analysis.format_type = detect_id_format(value);

    // Check if it's likely a machine ID
    analysis.is_likely_machine_id = is_likely_machine_id(value, &analysis.format_type);

    // Check if it appears to be hashed
    analysis.is_hashed = appears_hashed(value);

    // Assess privacy risk
    analysis.privacy_risk = assess_privacy_risk(&analysis);

    analysis
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IdAnalysis {
    pub is_likely_machine_id: bool,
    pub entropy_score: f64,
    pub format_type: IdFormat,
    pub privacy_risk: PrivacyRisk,
    pub is_hashed: bool,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum IdFormat {
    Uuid,           // UUID format (8-4-4-4-12)
    MacAddress,     // MAC address format
    HexString,      // Long hex string
    Base64,         // Base64 encoded
    Numeric,        // Pure numeric
    Mixed,          // Mixed alphanumeric
    Unknown,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum PrivacyRisk {
    Critical,   // Raw hardware ID being transmitted
    High,       // Likely machine ID with high entropy
    Medium,     // Possible machine ID or tracking token
    Low,        // Low entropy or clearly hashed
}

fn calculate_string_entropy(s: &str) -> f64 {
    if s.is_empty() {
        return 0.0;
    }

    let mut char_counts: HashMap<char, usize> = HashMap::new();
    for ch in s.chars() {
        *char_counts.entry(ch).or_insert(0) += 1;
    }

    let len = s.len() as f64;
    let mut entropy = 0.0;

    for &count in char_counts.values() {
        let probability = count as f64 / len;
        entropy -= probability * probability.log2();
    }

    entropy
}

fn detect_id_format(value: &str) -> IdFormat {
    let clean_value = value.trim();

    // UUID pattern (8-4-4-4-12)
    if regex::Regex::new(r"^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")
        .unwrap()
        .is_match(clean_value) {
        return IdFormat::Uuid;
    }

    // MAC address pattern
    if regex::Regex::new(r"^([0-9a-fA-F]{2}[:-]){5}[0-9a-fA-F]{2}$")
        .unwrap()
        .is_match(clean_value) {
        return IdFormat::MacAddress;
    }

    // Long hex string (likely hash)
    if regex::Regex::new(r"^[0-9a-fA-F]{32,}$")
        .unwrap()
        .is_match(clean_value) {
        return IdFormat::HexString;
    }

    // Base64 pattern
    if regex::Regex::new(r"^[A-Za-z0-9+/]{20,}={0,2}$")
        .unwrap()
        .is_match(clean_value) {
        return IdFormat::Base64;
    }

    // Pure numeric
    if clean_value.chars().all(|c| c.is_ascii_digit()) && clean_value.len() > 8 {
        return IdFormat::Numeric;
    }

    // Mixed alphanumeric
    if clean_value.len() > 8 && clean_value.chars().any(|c| c.is_ascii_alphabetic()) {
        return IdFormat::Mixed;
    }

    IdFormat::Unknown
}

fn is_likely_machine_id(value: &str, format: &IdFormat) -> bool {
    match format {
        IdFormat::Uuid => true,
        IdFormat::MacAddress => true,
        IdFormat::HexString => value.len() >= 32, // Likely hash or hardware serial
        IdFormat::Base64 => value.len() >= 20,    // Likely encoded ID
        IdFormat::Numeric => value.len() >= 10,   // Long numeric could be serial
        IdFormat::Mixed => value.len() >= 12,     // Mixed format with sufficient length
        IdFormat::Unknown => false,
    }
}

fn appears_hashed(value: &str) -> bool {
    // Check for common hash lengths and hex patterns
    let clean_value = value.trim();
    
    // SHA-256 (64 hex chars), SHA-1 (40 hex chars), MD5 (32 hex chars)
    if regex::Regex::new(r"^[0-9a-fA-F]{32}$|^[0-9a-fA-F]{40}$|^[0-9a-fA-F]{64}$")
        .unwrap()
        .is_match(clean_value) {
        return true;
    }

    // Base64 encoded hash patterns
    if regex::Regex::new(r"^[A-Za-z0-9+/]{43}=$|^[A-Za-z0-9+/]{27}=$|^[A-Za-z0-9+/]{87}==$")
        .unwrap()
        .is_match(clean_value) {
        return true;
    }

    false
}

fn assess_privacy_risk(analysis: &IdAnalysis) -> PrivacyRisk {
    // If it's hashed, lower the risk
    if analysis.is_hashed {
        return PrivacyRisk::Low;
    }

    // High entropy + machine ID format = high risk
    if analysis.is_likely_machine_id && analysis.entropy_score > 3.0 {
        match analysis.format_type {
            IdFormat::MacAddress => PrivacyRisk::Critical, // Raw MAC is critical
            IdFormat::Uuid => PrivacyRisk::High,           // UUID could be machine-specific
            IdFormat::HexString => PrivacyRisk::Medium,    // Could be serial number
            _ => PrivacyRisk::Medium,
        }
    } else if analysis.is_likely_machine_id {
        PrivacyRisk::Medium
    } else {
        PrivacyRisk::Low
    }
}
