use clap::{Arg, Command};
use colored::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::time::SystemTime;
use chrono::{DateTime, Local};
use uuid;

mod patterns;
mod scanner;
mod watcher;

use patterns::TelemetryPatterns;
use scanner::FileScanner;
use watcher::FileWatcher;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Detection {
    pub file_path: PathBuf,
    pub line_number: usize,
    pub content: String,
    pub pattern_type: String,
    pub severity: String,
    pub timestamp: SystemTime,
    pub context_lines: Vec<String>,
    pub parent_structure: Option<String>,
    pub child_references: Vec<String>,
    pub obfuscation_level: String,
    pub data_flow_analysis: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ScanResult {
    pub detections: Vec<Detection>,
    pub scan_time: SystemTime,
    pub files_scanned: usize,
    pub directories_scanned: usize,
    pub penetration_level: String,
    pub structure_analysis: HashMap<String, Vec<String>>,
    pub data_flow_map: HashMap<String, Vec<String>>,
    pub obfuscation_patterns: Vec<String>,
    pub scan_id: String,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let matches = Command::new("Advanced Telemetry & Machine ID Watcher")
        .version("2.0")
        .author("Advanced Security Scanner")
        .about("High-penetration scanner for telemetry, machine ID, and complex data structures")
        .arg(
            Arg::new("path")
                .short('p')
                .long("path")
                .value_name("PATH")
                .help("Path to scan (default: C:\\Users)")
                .default_value("C:\\Users"),
        )
        .arg(
            Arg::new("watch")
                .short('w')
                .long("watch")
                .help("Enable file watching mode")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("penetration-level")
                .short('l')
                .long("penetration-level")
                .value_name("LEVEL")
                .help("Penetration level: low, medium, high, extreme")
                .default_value("high"),
        )
        .arg(
            Arg::new("deep-scan")
                .long("deep-scan")
                .help("Enable deep structure analysis")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("analyze-structures")
                .long("analyze-structures")
                .help("Analyze parent-child relationships and data flows")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("extensions")
                .short('e')
                .long("extensions")
                .value_name("EXTS")
                .help("File extensions to scan (comma-separated)")
                .default_value("rs,py,js,ts,cpp,c,h,hpp,cs,java,go"),
        )
        .get_matches();

    let scan_path = matches.get_one::<String>("path").unwrap();
    let watch_mode = matches.get_flag("watch");
    let penetration_level = matches.get_one::<String>("penetration-level").unwrap();
    let deep_scan = matches.get_flag("deep-scan");
    let analyze_structures = matches.get_flag("analyze-structures");
    let extensions: Vec<&str> = matches
        .get_one::<String>("extensions")
        .unwrap()
        .split(',')
        .collect();

    // Generate unique datetime filename for auto-save
    let now: DateTime<Local> = Local::now();
    let auto_save_filename = format!("telemetry_scan_{}_{}.json",
        now.format("%Y%m%d_%H%M%S"),
        uuid::Uuid::new_v4().to_string()[..8].to_string()
    );

    println!("{}", "🔍 Advanced Telemetry & Machine ID Watcher".bright_cyan().bold());
    println!("Scanning path: {}", scan_path.bright_yellow());
    println!("Extensions: {}", extensions.join(", ").bright_green());
    println!("Penetration level: {}", penetration_level.bright_magenta());
    println!("Auto-save file: {}", auto_save_filename.bright_green());

    let patterns = TelemetryPatterns::new_advanced(penetration_level);
    let scanner = FileScanner::new_advanced(patterns, extensions, deep_scan, analyze_structures);

    // Initial scan
    println!("\n{}", "📊 Starting high-penetration scan...".bright_blue());
    let scan_result = scanner.scan_directory_advanced(Path::new(scan_path)).await?;
    
    display_results(&scan_result);

    // Always auto-save results with unique datetime filename
    save_results(&scan_result, &auto_save_filename)?;
    println!("📁 Results automatically saved to: {}", auto_save_filename.bright_green().bold());

    if watch_mode {
        println!("\n{}", "👁️  Starting file watcher...".bright_magenta());
        let watcher = FileWatcher::new(scanner);
        watcher.watch(Path::new(scan_path)).await?;
    }

    Ok(())
}

fn display_results(result: &ScanResult) {
    println!("\n{}", "📋 SCAN RESULTS".bright_cyan().bold());
    println!("Files scanned: {}", result.files_scanned.to_string().bright_yellow());
    println!("Directories scanned: {}", result.directories_scanned.to_string().bright_yellow());
    println!("Detections found: {}", result.detections.len().to_string().bright_red().bold());

    if !result.detections.is_empty() {
        println!("\n{}", "🚨 DETECTIONS:".bright_red().bold());
        
        let mut by_type: HashMap<String, Vec<&Detection>> = HashMap::new();
        for detection in &result.detections {
            by_type.entry(detection.pattern_type.clone())
                .or_insert_with(Vec::new)
                .push(detection);
        }

        for (pattern_type, detections) in by_type {
            println!("\n{} ({})", pattern_type.bright_yellow().bold(), detections.len());
            for detection in detections.iter().take(10) { // Limit to first 10 per type
                let severity_color = match detection.severity.as_str() {
                    "HIGH" => "red",
                    "MEDIUM" => "yellow",
                    "LOW" => "green",
                    _ => "white",
                };
                
                println!("  {} {}:{}",
                    detection.severity.color(severity_color).bold(),
                    detection.file_path.display().to_string().bright_blue(),
                    detection.line_number.to_string().bright_white()
                );
                println!("    {}", detection.content.trim().bright_white());
            }
            if detections.len() > 10 {
                println!("    ... and {} more", (detections.len() - 10).to_string().bright_cyan());
            }
        }
    }
}

fn save_results(result: &ScanResult, output_path: &str) -> Result<(), Box<dyn std::error::Error>> {
    let json = serde_json::to_string_pretty(result)?;
    fs::write(output_path, json)?;
    Ok(())
}
