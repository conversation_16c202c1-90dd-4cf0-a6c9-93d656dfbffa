use regex::Regex;
use std::collections::HashMap;

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct PatternInfo {
    pub regex: Regex,
    pub description: String,
    pub severity: String,
    pub category: String,
}

#[derive(Debug)]
pub struct TelemetryPatterns {
    pub patterns: HashMap<String, PatternInfo>,
}

impl TelemetryPatterns {
    pub fn new() -> Self {
        Self::new_advanced("medium")
    }

    pub fn new_advanced(penetration_level: &str) -> Self {
        let mut patterns = HashMap::new();

        // Add base patterns
        Self::add_base_patterns(&mut patterns);

        // Add advanced patterns based on penetration level
        match penetration_level {
            "low" => {},
            "medium" => Self::add_medium_patterns(&mut patterns),
            "high" => {
                Self::add_medium_patterns(&mut patterns);
                Self::add_high_patterns(&mut patterns);
            },
            "extreme" => {
                Self::add_medium_patterns(&mut patterns);
                Self::add_high_patterns(&mut patterns);
                Self::add_extreme_patterns(&mut patterns);
            },
            _ => Self::add_medium_patterns(&mut patterns),
        }

        TelemetryPatterns { patterns }
    }

    fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {

        // Basic Machine ID patterns
        Self::add_pattern(patterns, "machine_id_generation",
            r"(?i)(machine[_-]?id|device[_-]?id|hardware[_-]?id|system[_-]?id|unique[_-]?id)",
            "Machine/Device ID generation or usage",
            "HIGH",
            "MACHINE_ID"
        );

        Self::add_pattern(patterns, "uuid_generation",
            r"(?i)(uuid::v[1-5]|Uuid::new|uuid\.new|generate[_-]?uuid)",
            "UUID generation for identification",
            "MEDIUM",
            "MACHINE_ID"
        );

        Self::add_pattern(patterns, "mac_address",
            r"(?i)(mac[_-]?address|get[_-]?mac|network[_-]?interface)",
            "MAC address collection",
            "HIGH",
            "MACHINE_ID"
        );

        Self::add_pattern(patterns, "cpu_info",
            r"(?i)(cpu[_-]?info|processor[_-]?id|cpu[_-]?serial|cpuid)",
            "CPU information collection",
            "HIGH",
            "MACHINE_ID"
        );

        Self::add_pattern(patterns, "disk_serial",
            r"(?i)(disk[_-]?serial|drive[_-]?id|volume[_-]?serial)",
            "Disk/Drive serial number collection",
            "HIGH",
            "MACHINE_ID"
        );

        // Basic Telemetry patterns
        Self::add_pattern(patterns, "telemetry_collection",
            r"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)",
            "Telemetry/Analytics collection",
            "HIGH",
            "TELEMETRY"
        );

        Self::add_pattern(patterns, "data_transmission",
            r"(?i)(send[_-]?data|transmit|upload[_-]?stats|report[_-]?usage)",
            "Data transmission to external servers",
            "HIGH",
            "TELEMETRY"
        );
    }

    fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {
        // Medium penetration patterns
        Self::add_pattern(patterns, "user_behavior",
            r"(?i)(user[_-]?behavior|click[_-]?tracking|session[_-]?data|activity[_-]?log)",
            "User behavior tracking",
            "MEDIUM",
            "TELEMETRY"
        );

        Self::add_pattern(patterns, "crash_reporting",
            r"(?i)(crash[_-]?report|error[_-]?report|exception[_-]?tracking)",
            "Crash/Error reporting",
            "MEDIUM",
            "TELEMETRY"
        );

        // System information patterns
        Self::add_pattern(patterns, "system_info",
            r"(?i)(system[_-]?info|os[_-]?version|platform[_-]?info|environment[_-]?info)",
            "System information collection",
            "MEDIUM",
            "SYSTEM_INFO"
        );

        Self::add_pattern(patterns, "registry_access",
            r"(?i)(registry|hkey_|regkey|winreg)",
            "Windows Registry access",
            "HIGH",
            "SYSTEM_INFO"
        );

        Self::add_pattern(patterns, "wmi_queries",
            r"(?i)(wmi|win32_|cim_|select.*from.*win32)",
            "WMI queries for system information",
            "HIGH",
            "SYSTEM_INFO"
        );
    }

    fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {
        // High penetration patterns - obfuscated and complex structures
        Self::add_pattern(patterns, "obfuscated_ids",
            r"(?i)(base64|hex|encode|decode|encrypt|decrypt|hash|sha|md5).*[_-]?(id|uid|key)",
            "Obfuscated ID generation/processing",
            "HIGH",
            "OBFUSCATED"
        );

        Self::add_pattern(patterns, "complex_structures",
            r"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)",
            "Complex data structures for tracking",
            "HIGH",
            "STRUCTURE"
        );

        Self::add_pattern(patterns, "parent_child_refs",
            r"(?i)(parent|child|hierarchy|tree|node).*[_-]?(id|ref|link)",
            "Parent-child relationship patterns",
            "MEDIUM",
            "STRUCTURE"
        );

        Self::add_pattern(patterns, "data_flow_patterns",
            r"(?i)(pipeline|stream|flow|chain|sequence).*[_-]?(data|info|track)",
            "Data flow analysis patterns",
            "MEDIUM",
            "DATA_FLOW"
        );

        // Network patterns
        Self::add_pattern(patterns, "network_requests",
            r"(?i)(http[s]?://|api[_-]?call|rest[_-]?client|web[_-]?request)",
            "Network requests (potential data transmission)",
            "MEDIUM",
            "NETWORK"
        );

        Self::add_pattern(patterns, "external_domains",
            r"(?i)(\.com|\.net|\.org|\.io|analytics|telemetry|tracking)",
            "External domain references",
            "LOW",
            "NETWORK"
        );

        // File system patterns
        Self::add_pattern(patterns, "user_directories",
            r"(?i)(users/|\\users\\|home/|appdata|documents|desktop)",
            "User directory access",
            "MEDIUM",
            "FILESYSTEM"
        );

        Self::add_pattern(patterns, "config_files",
            r"(?i)(\.config|\.settings|\.ini|\.json|\.xml|\.plist)",
            "Configuration file access",
            "LOW",
            "FILESYSTEM"
        );

        // Fingerprint-specific patterns
        Self::add_pattern(patterns, "local_fingerprint",
            r"(?i)(machine_fingerprint|generate_fingerprint|local[_-]?fingerprint)",
            "Local fingerprint generation (monitor for transmission)",
            "CRITICAL",
            "LOCAL_FINGERPRINT"
        );

        Self::add_pattern(patterns, "fingerprint_transmission",
            r"(?i)(send[_-]?fingerprint|transmit[_-]?fingerprint|upload[_-]?fingerprint)",
            "Fingerprint transmission to external servers",
            "CRITICAL",
            "FINGERPRINT_TRANSMISSION"
        );

        Self::add_pattern(patterns, "hash_functions",
            r"(?i)(sha256|sha1|md5|blake2|hash[_-]?digest)",
            "Hash function usage (good for privacy)",
            "LOW",
            "PRIVACY_PROTECTION"
        );

        // Rust-specific patterns
        Self::add_pattern(patterns, "rust_sysinfo",
            r"(?i)(sysinfo::|System::new|get_processor|get_networks)",
            "Rust sysinfo crate usage",
            "HIGH",
            "RUST_SPECIFIC"
        );

        Self::add_pattern(patterns, "rust_machine_uid",
            r"(?i)(machine_uid|get_machine_id)",
            "Rust machine-uid crate usage",
            "HIGH",
            "RUST_SPECIFIC"
        );

        Self::add_pattern(patterns, "rust_reqwest",
            r"(?i)(reqwest::|Client::new|post|get.*http)",
            "Rust reqwest HTTP client usage",
            "MEDIUM",
            "RUST_SPECIFIC"
        );
    }

    fn add_extreme_patterns(patterns: &mut HashMap<String, PatternInfo>) {
        // Extreme penetration - advanced obfuscation and steganography
        Self::add_pattern(patterns, "steganography",
            r"(?i)(steganography|hidden[_-]?data|embed|conceal|disguise)",
            "Steganographic data hiding",
            "EXTREME",
            "STEGANOGRAPHY"
        );

        Self::add_pattern(patterns, "polymorphic_code",
            r"(?i)(polymorphic|metamorphic|self[_-]?modify|dynamic[_-]?code)",
            "Polymorphic/self-modifying code",
            "EXTREME",
            "POLYMORPHIC"
        );

        Self::add_pattern(patterns, "anti_analysis",
            r"(?i)(anti[_-]?debug|anti[_-]?vm|detect[_-]?sandbox|evasion)",
            "Anti-analysis techniques",
            "EXTREME",
            "EVASION"
        );

        Self::add_pattern(patterns, "deep_system_hooks",
            r"(?i)(hook|inject|patch|syscall|kernel[_-]?mode|rootkit)",
            "Deep system hooks and injections",
            "EXTREME",
            "SYSTEM_HOOK"
        );

        Self::add_pattern(patterns, "encrypted_payloads",
            r"(?i)(payload|shellcode|encrypted[_-]?data|cipher|cryptographic)",
            "Encrypted payloads and data",
            "EXTREME",
            "ENCRYPTION"
        );
    }

    fn add_pattern(
        patterns: &mut HashMap<String, PatternInfo>,
        name: &str,
        pattern: &str,
        description: &str,
        severity: &str,
        category: &str,
    ) {
        if let Ok(regex) = Regex::new(pattern) {
            patterns.insert(
                name.to_string(),
                PatternInfo {
                    regex,
                    description: description.to_string(),
                    severity: severity.to_string(),
                    category: category.to_string(),
                },
            );
        }
    }

    pub fn scan_content(&self, content: &str) -> Vec<(String, &PatternInfo, Vec<usize>)> {
        let mut matches = Vec::new();
        
        for (name, pattern_info) in &self.patterns {
            let line_matches: Vec<usize> = content
                .lines()
                .enumerate()
                .filter_map(|(line_num, line)| {
                    if pattern_info.regex.is_match(line) {
                        Some(line_num + 1)
                    } else {
                        None
                    }
                })
                .collect();

            if !line_matches.is_empty() {
                matches.push((name.clone(), pattern_info, line_matches));
            }
        }

        matches
    }
}
