use crate::{<PERSON><PERSON><PERSON>, ScanResult, TelemetryPatterns, PrivacySummary};
use crate::fingerprint::{MachineFingerprint, analyze_id_string, PrivacyRisk};
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use std::time::SystemTime;
use tokio::task;
use walkdir::WalkDir;
use rusqlite::Connection;

pub struct FileScanner {
    patterns: TelemetryPatterns,
    extensions: Vec<String>,
    deep_scan: bool,
    analyze_structures: bool,
    penetration_level: String,
    privacy_analysis: bool,
    machine_fingerprint: MachineFingerprint,
}

impl FileScanner {
    pub fn new(patterns: TelemetryPatterns, extensions: Vec<&str>) -> Self {
        Self {
            patterns,
            extensions: extensions.iter().map(|s| s.to_string()).collect(),
            deep_scan: false,
            analyze_structures: false,
            penetration_level: "medium".to_string(),
            privacy_analysis: false,
            machine_fingerprint: MachineFingerprint::generate(),
        }
    }

    pub fn new_advanced(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool) -> Self {
        Self::new_with_privacy(patterns, extensions, deep_scan, analyze_structures, false)
    }

    pub fn new_with_privacy(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool, privacy_analysis: bool) -> Self {
        Self {
            patterns,
            extensions: extensions.iter().map(|s| s.to_string()).collect(),
            deep_scan,
            analyze_structures,
            penetration_level: "high".to_string(),
            privacy_analysis,
            machine_fingerprint: MachineFingerprint::generate(),
        }
    }

    pub fn supports_extension(&self, extension: &str) -> bool {
        self.extensions.contains(&extension.to_lowercase())
    }

    pub async fn scan_directory(&self, path: &Path) -> Result<ScanResult, Box<dyn std::error::Error>> {
        let mut detections = Vec::new();
        let mut files_scanned = 0;
        let mut directories_scanned = 0;
        let scan_time = SystemTime::now();

        println!("🔍 Scanning directory: {}", path.display());

        for entry in WalkDir::new(path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_dir() {
                directories_scanned += 1;
                continue;
            }

            let file_path = entry.path();
            
            // Check if file should be scanned
            if !self.should_scan_file(&file_path) {
                continue;
            }

            files_scanned += 1;
            
            // Scan file content
            match self.scan_file(file_path).await {
                Ok(mut file_detections) => {
                    detections.append(&mut file_detections);
                }
                Err(e) => {
                    eprintln!("Error scanning {}: {}", file_path.display(), e);
                }
            }

            // Print progress every 100 files
            if files_scanned % 100 == 0 {
                println!("📄 Scanned {} files, {} detections so far", files_scanned, detections.len());
            }
        }

        // Generate privacy summary
        let privacy_summary = self.generate_privacy_summary(&detections);

        Ok(ScanResult {
            detections,
            scan_time,
            files_scanned,
            directories_scanned,
            penetration_level: self.penetration_level.clone(),
            structure_analysis: HashMap::new(),
            data_flow_map: HashMap::new(),
            obfuscation_patterns: Vec::new(),
            scan_id: uuid::Uuid::new_v4().to_string(),
            machine_fingerprint: self.machine_fingerprint.fingerprint().to_string(),
            fingerprint_entropy: self.machine_fingerprint.entropy_score(),
            privacy_summary,
        })
    }

    pub async fn scan_file(&self, file_path: &Path) -> Result<Vec<Detection>, Box<dyn std::error::Error>> {
        // Check file extension to determine scan method
        match file_path.extension().and_then(|s| s.to_str()) {
            Some("db") | Some("sqlite") | Some("sqlite3") => {
                // Scan SQLite database
                self.scan_sqlite_file(file_path).await
            }
            _ => {
                // Scan as text file
                self.scan_text_file(file_path).await
            }
        }
    }

    async fn scan_text_file(&self, file_path: &Path) -> Result<Vec<Detection>, Box<dyn std::error::Error>> {
        let content = task::spawn_blocking({
            let file_path = file_path.to_owned();
            move || fs::read_to_string(&file_path)
        }).await??;

        let mut detections = Vec::new();
        let matches = self.patterns.scan_content(&content);

        for (_pattern_name, pattern_info, line_numbers) in matches {
            for line_number in line_numbers {
                let line_content = content
                    .lines()
                    .nth(line_number - 1)
                    .unwrap_or("")
                    .to_string();

                // Analyze the line content for ID patterns if privacy analysis is enabled
                let (id_analysis, data_collected, is_hashed, privacy_risk, entropy_score) =
                    if self.privacy_analysis {
                        let analysis = analyze_id_string(&line_content);
                        let data_type = self.detect_data_type(&line_content, &pattern_info.category);
                        let privacy_risk_str = match analysis.privacy_risk {
                            PrivacyRisk::Critical => "CRITICAL",
                            PrivacyRisk::High => "HIGH",
                            PrivacyRisk::Medium => "MEDIUM",
                            PrivacyRisk::Low => "LOW",
                        };
                        (Some(analysis.clone()), data_type, analysis.is_hashed, privacy_risk_str.to_string(), analysis.entropy_score)
                    } else {
                        (None, None, false, "UNKNOWN".to_string(), 0.0)
                    };

                detections.push(Detection {
                    file_path: file_path.to_path_buf(),
                    line_number,
                    content: line_content.clone(),
                    pattern_type: format!("{} ({})", pattern_info.description, pattern_info.category),
                    severity: pattern_info.severity.clone(),
                    timestamp: SystemTime::now(),
                    context_lines: self.get_context_lines(&content, line_number, 3),
                    parent_structure: self.analyze_parent_structure(&content, line_number),
                    child_references: self.find_child_references(&content, &line_content),
                    obfuscation_level: self.detect_obfuscation(&line_content),
                    data_flow_analysis: self.analyze_data_flow(&content, line_number),
                    data_collected,
                    is_hashed,
                    privacy_risk,
                    entropy_score,
                    id_analysis,
                });
            }
        }

        Ok(detections)
    }

    async fn scan_sqlite_file(&self, file_path: &Path) -> Result<Vec<Detection>, Box<dyn std::error::Error>> {
        let mut detections = Vec::new();

        // Try to open the SQLite database
        let conn = match Connection::open(file_path) {
            Ok(conn) => conn,
            Err(e) => {
                // If it's not a valid SQLite file, try scanning as text
                eprintln!("Failed to open {} as SQLite database: {}, trying as text", file_path.display(), e);
                return self.scan_text_file(file_path).await;
            }
        };

        // Get all table names
        let table_names: Result<Vec<String>, rusqlite::Error> = conn
            .prepare("SELECT name FROM sqlite_master WHERE type='table'")?
            .query_map([], |row| row.get(0))?
            .collect();

        let table_names = match table_names {
            Ok(names) => names,
            Err(e) => {
                eprintln!("Error reading tables from {}: {}", file_path.display(), e);
                return Ok(detections);
            }
        };

        // Scan each table
        for table_name in table_names {
            if let Err(e) = self.scan_sqlite_table(&conn, file_path, &table_name, &mut detections) {
                eprintln!("Error scanning table {} in {}: {}", table_name, file_path.display(), e);
            }
        }

        Ok(detections)
    }

    fn scan_sqlite_table(
        &self,
        conn: &Connection,
        file_path: &Path,
        table_name: &str,
        detections: &mut Vec<Detection>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Get column information
        let mut stmt = conn.prepare(&format!("PRAGMA table_info({})", table_name))?;
        let column_info: Vec<(i32, String, String)> = stmt
            .query_map([], |row| {
                Ok((
                    row.get::<_, i32>(0)?,      // cid
                    row.get::<_, String>(1)?,   // name
                    row.get::<_, String>(2)?,   // type
                ))
            })?
            .collect::<Result<Vec<_>, _>>()?;

        // Query the table data (limit to first 1000 rows for performance)
        let query = format!("SELECT * FROM {} LIMIT 1000", table_name);
        let mut stmt = conn.prepare(&query)?;

        let rows = stmt.query_map([], |row| {
            let mut row_data = Vec::new();
            for (col_idx, _col_name, _col_type) in &column_info {
                let value: String = match row.get::<_, rusqlite::types::Value>(*col_idx as usize) {
                    Ok(rusqlite::types::Value::Text(s)) => s,
                    Ok(rusqlite::types::Value::Integer(i)) => i.to_string(),
                    Ok(rusqlite::types::Value::Real(f)) => f.to_string(),
                    Ok(rusqlite::types::Value::Blob(b)) => format!("BLOB({} bytes)", b.len()),
                    Ok(rusqlite::types::Value::Null) => "NULL".to_string(),
                    Err(_) => "ERROR".to_string(),
                };
                row_data.push(value);
            }
            Ok(row_data)
        })?;

        // Scan each row
        for (row_idx, row_result) in rows.enumerate() {
            let row_data = row_result?;

            for (col_idx, (_, col_name, _)) in column_info.iter().enumerate() {
                if col_idx < row_data.len() {
                    let cell_value = &row_data[col_idx];

                    // Scan cell content against patterns
                    let matches = self.patterns.scan_content(cell_value);

                    for (_pattern_name, pattern_info, _line_numbers) in matches {
                        // Analyze the cell content for ID patterns if privacy analysis is enabled
                        let (id_analysis, data_collected, is_hashed, privacy_risk, entropy_score) =
                            if self.privacy_analysis {
                                let analysis = analyze_id_string(cell_value);
                                let data_type = self.detect_data_type(cell_value, &pattern_info.category);
                                let privacy_risk_str = match analysis.privacy_risk {
                                    PrivacyRisk::Critical => "CRITICAL",
                                    PrivacyRisk::High => "HIGH",
                                    PrivacyRisk::Medium => "MEDIUM",
                                    PrivacyRisk::Low => "LOW",
                                };
                                (Some(analysis.clone()), data_type, analysis.is_hashed, privacy_risk_str.to_string(), analysis.entropy_score)
                            } else {
                                (None, None, false, "UNKNOWN".to_string(), 0.0)
                            };

                        detections.push(Detection {
                            file_path: file_path.to_path_buf(),
                            line_number: row_idx + 1, // Use row number as line number
                            content: format!("{}:{} = {}", table_name, col_name, cell_value),
                            pattern_type: format!("{} ({})", pattern_info.description, pattern_info.category),
                            severity: pattern_info.severity.clone(),
                            timestamp: SystemTime::now(),
                            context_lines: vec![format!("SQLite Table: {}, Column: {}, Row: {}", table_name, col_name, row_idx + 1)],
                            parent_structure: Some(format!("SQLite Table: {}", table_name)),
                            child_references: Vec::new(),
                            obfuscation_level: "0".to_string(),
                            data_flow_analysis: Some(format!("Database field: {}.{}", table_name, col_name)),
                            data_collected,
                            is_hashed,
                            privacy_risk,
                            entropy_score,
                            id_analysis,
                        });
                    }
                }
            }
        }

        Ok(())
    }

    pub async fn scan_directory_advanced(&self, path: &Path) -> Result<ScanResult, Box<dyn std::error::Error>> {
        // Use the existing scan_directory method for now
        self.scan_directory(path).await
    }

    fn get_context_lines(&self, content: &str, line_number: usize, context_size: usize) -> Vec<String> {
        let lines: Vec<&str> = content.lines().collect();
        let start = if line_number > context_size { line_number - context_size } else { 0 };
        let end = std::cmp::min(line_number + context_size, lines.len());

        lines[start..end].iter().map(|s| s.to_string()).collect()
    }

    fn analyze_parent_structure(&self, content: &str, line_number: usize) -> Option<String> {
        if !self.analyze_structures {
            return None;
        }

        let lines: Vec<&str> = content.lines().collect();
        let mut current_line = if line_number > 0 { line_number - 1 } else { 0 };

        // Look backwards for struct, class, impl, or function definitions
        while current_line > 0 {
            let line = lines[current_line];
            if line.contains("struct ") || line.contains("class ") ||
               line.contains("impl ") || line.contains("fn ") {
                return Some(line.trim().to_string());
            }
            current_line -= 1;
        }

        None
    }

    fn find_child_references(&self, _content: &str, current_line: &str) -> Vec<String> {
        if !self.analyze_structures {
            return Vec::new();
        }

        let mut references = Vec::new();

        // Look for variable assignments, function calls, or method calls in the current line
        if let Some(captures) = regex::Regex::new(r"(\w+)\s*[=:]\s*").unwrap().captures(current_line) {
            if let Some(var_name) = captures.get(1) {
                references.push(var_name.as_str().to_string());
            }
        }

        // Look for function/method calls
        if let Some(captures) = regex::Regex::new(r"(\w+)\s*\(").unwrap().captures(current_line) {
            if let Some(func_name) = captures.get(1) {
                references.push(format!("{}()", func_name.as_str()));
            }
        }

        references
    }

    fn detect_obfuscation(&self, line: &str) -> String {
        let mut obfuscation_score = 0;

        // Check for base64-like patterns
        if regex::Regex::new(r"[A-Za-z0-9+/]{20,}={0,2}").unwrap().is_match(line) {
            obfuscation_score += 3;
        }

        // Check for hex patterns
        if regex::Regex::new(r"0x[0-9a-fA-F]{8,}").unwrap().is_match(line) {
            obfuscation_score += 2;
        }

        // Check for unusual character sequences
        if regex::Regex::new(r"[^a-zA-Z0-9\s]{5,}").unwrap().is_match(line) {
            obfuscation_score += 1;
        }

        match obfuscation_score {
            0 => "NONE".to_string(),
            1..=2 => "LOW".to_string(),
            3..=4 => "MEDIUM".to_string(),
            _ => "HIGH".to_string(),
        }
    }

    fn analyze_data_flow(&self, content: &str, line_number: usize) -> Option<String> {
        if !self.deep_scan {
            return None;
        }

        let lines: Vec<&str> = content.lines().collect();
        let current_line = lines.get(line_number.saturating_sub(1))?;

        // Simple data flow analysis - look for assignments and function calls
        if current_line.contains("=") && (current_line.contains("send") || current_line.contains("transmit")) {
            return Some("DATA_TRANSMISSION".to_string());
        }

        if current_line.contains("collect") || current_line.contains("gather") {
            return Some("DATA_COLLECTION".to_string());
        }

        if current_line.contains("encrypt") || current_line.contains("encode") {
            return Some("DATA_PROCESSING".to_string());
        }

        None
    }

    fn detect_data_type(&self, line_content: &str, category: &str) -> Option<String> {
        // Detect what type of data is being collected based on patterns
        match category {
            "MACHINE_ID" => {
                if line_content.to_lowercase().contains("mac") {
                    Some("mac_address".to_string())
                } else if line_content.to_lowercase().contains("cpu") {
                    Some("cpu_info".to_string())
                } else if line_content.to_lowercase().contains("disk") {
                    Some("disk_serial".to_string())
                } else if line_content.to_lowercase().contains("uuid") {
                    Some("uuid".to_string())
                } else {
                    Some("machine_identifier".to_string())
                }
            }
            "TELEMETRY" => Some("telemetry_data".to_string()),
            "SYSTEM_INFO" => Some("system_information".to_string()),
            "NETWORK" => Some("network_data".to_string()),
            _ => None,
        }
    }

    fn generate_privacy_summary(&self, detections: &[Detection]) -> PrivacySummary {
        let mut summary = PrivacySummary {
            critical_risks: 0,
            high_risks: 0,
            medium_risks: 0,
            low_risks: 0,
            raw_ids_detected: 0,
            hashed_ids_detected: 0,
            fingerprint_transmissions: 0,
        };

        for detection in detections {
            // Count privacy risks
            match detection.privacy_risk.as_str() {
                "CRITICAL" => summary.critical_risks += 1,
                "HIGH" => summary.high_risks += 1,
                "MEDIUM" => summary.medium_risks += 1,
                "LOW" => summary.low_risks += 1,
                _ => {}
            }

            // Count hashed vs raw IDs
            if detection.is_hashed {
                summary.hashed_ids_detected += 1;
            } else if detection.data_collected.is_some() {
                summary.raw_ids_detected += 1;
            }

            // Check for fingerprint transmission patterns
            if detection.content.to_lowercase().contains("transmit") ||
               detection.content.to_lowercase().contains("send") ||
               detection.content.to_lowercase().contains("post") {
                if detection.data_collected.is_some() {
                    summary.fingerprint_transmissions += 1;
                }
            }
        }

        summary
    }

    fn should_scan_file(&self, file_path: &Path) -> bool {
        // Check file size first (15MB default limit)
        const MAX_FILE_SIZE: u64 = 15 * 1024 * 1024; // 15 MB

        if let Ok(metadata) = file_path.metadata() {
            if metadata.len() > MAX_FILE_SIZE {
                return false;
            }
        }

        // Always scan database files
        if let Some(extension) = file_path.extension() {
            if let Some(ext_str) = extension.to_str() {
                let ext_lower = ext_str.to_lowercase();

                // Database files
                if matches!(ext_lower.as_str(), "db" | "sqlite" | "sqlite3" | "vscdb") {
                    return true;
                }

                // VS Code specific files
                if matches!(ext_lower.as_str(), "json" | "log" | "txt") {
                    // Check if it's in a VS Code directory
                    if crate::vscode::is_vscode_extension_path(&file_path.to_path_buf()) {
                        return true;
                    }

                    // Check for VS Code telemetry file names
                    if let Some(file_name) = file_path.file_name() {
                        if let Some(name_str) = file_name.to_str() {
                            let telemetry_files = crate::vscode::get_vscode_telemetry_files();
                            if telemetry_files.iter().any(|&tf| name_str.contains(tf)) {
                                return true;
                            }
                        }
                    }
                }

                // Standard extensions
                if self.extensions.contains(&ext_lower) {
                    return true;
                }
            }
        }

        // Files without extensions in VS Code directories
        if crate::vscode::is_vscode_extension_path(&file_path.to_path_buf()) {
            if let Some(file_name) = file_path.file_name() {
                if let Some(name_str) = file_name.to_str() {
                    let telemetry_files = crate::vscode::get_vscode_telemetry_files();
                    if telemetry_files.iter().any(|&tf| name_str == tf) {
                        return true;
                    }
                }
            }
        }

        false
    }
}
