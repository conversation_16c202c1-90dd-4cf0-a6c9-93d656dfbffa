use crate::{Detection, ScanResult, TelemetryPatterns};
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use std::time::SystemTime;
use tokio::task;
use walkdir::WalkDir;

pub struct FileScanner {
    patterns: TelemetryPatterns,
    extensions: Vec<String>,
    deep_scan: bool,
    analyze_structures: bool,
    penetration_level: String,
}

impl FileScanner {
    pub fn new(patterns: TelemetryPatterns, extensions: Vec<&str>) -> Self {
        Self {
            patterns,
            extensions: extensions.iter().map(|s| s.to_string()).collect(),
            deep_scan: false,
            analyze_structures: false,
            penetration_level: "medium".to_string(),
        }
    }

    pub fn new_advanced(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool) -> Self {
        Self {
            patterns,
            extensions: extensions.iter().map(|s| s.to_string()).collect(),
            deep_scan,
            analyze_structures,
            penetration_level: "high".to_string(),
        }
    }

    pub fn supports_extension(&self, extension: &str) -> bool {
        self.extensions.contains(&extension.to_lowercase())
    }

    pub async fn scan_directory(&self, path: &Path) -> Result<ScanResult, Box<dyn std::error::Error>> {
        let mut detections = Vec::new();
        let mut files_scanned = 0;
        let mut directories_scanned = 0;
        let scan_time = SystemTime::now();

        println!("🔍 Scanning directory: {}", path.display());

        for entry in WalkDir::new(path)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            if entry.file_type().is_dir() {
                directories_scanned += 1;
                continue;
            }

            let file_path = entry.path();
            
            // Check if file has one of the target extensions
            if let Some(extension) = file_path.extension() {
                if let Some(ext_str) = extension.to_str() {
                    if !self.extensions.contains(&ext_str.to_lowercase()) {
                        continue;
                    }
                } else {
                    continue;
                }
            } else {
                continue;
            }

            files_scanned += 1;
            
            // Scan file content
            match self.scan_file(file_path).await {
                Ok(mut file_detections) => {
                    detections.append(&mut file_detections);
                }
                Err(e) => {
                    eprintln!("Error scanning {}: {}", file_path.display(), e);
                }
            }

            // Print progress every 100 files
            if files_scanned % 100 == 0 {
                println!("📄 Scanned {} files, {} detections so far", files_scanned, detections.len());
            }
        }

        Ok(ScanResult {
            detections,
            scan_time,
            files_scanned,
            directories_scanned,
            penetration_level: self.penetration_level.clone(),
            structure_analysis: HashMap::new(),
            data_flow_map: HashMap::new(),
            obfuscation_patterns: Vec::new(),
            scan_id: uuid::Uuid::new_v4().to_string(),
        })
    }

    pub async fn scan_file(&self, file_path: &Path) -> Result<Vec<Detection>, Box<dyn std::error::Error>> {
        let content = task::spawn_blocking({
            let file_path = file_path.to_owned();
            move || fs::read_to_string(&file_path)
        }).await??;

        let mut detections = Vec::new();
        let matches = self.patterns.scan_content(&content);

        for (_pattern_name, pattern_info, line_numbers) in matches {
            for line_number in line_numbers {
                let line_content = content
                    .lines()
                    .nth(line_number - 1)
                    .unwrap_or("")
                    .to_string();

                detections.push(Detection {
                    file_path: file_path.to_path_buf(),
                    line_number,
                    content: line_content.clone(),
                    pattern_type: format!("{} ({})", pattern_info.description, pattern_info.category),
                    severity: pattern_info.severity.clone(),
                    timestamp: SystemTime::now(),
                    context_lines: self.get_context_lines(&content, line_number, 3),
                    parent_structure: self.analyze_parent_structure(&content, line_number),
                    child_references: self.find_child_references(&content, &line_content),
                    obfuscation_level: self.detect_obfuscation(&line_content),
                    data_flow_analysis: self.analyze_data_flow(&content, line_number),
                });
            }
        }

        Ok(detections)
    }

    pub async fn scan_directory_advanced(&self, path: &Path) -> Result<ScanResult, Box<dyn std::error::Error>> {
        // Use the existing scan_directory method for now
        self.scan_directory(path).await
    }

    fn get_context_lines(&self, content: &str, line_number: usize, context_size: usize) -> Vec<String> {
        let lines: Vec<&str> = content.lines().collect();
        let start = if line_number > context_size { line_number - context_size } else { 0 };
        let end = std::cmp::min(line_number + context_size, lines.len());

        lines[start..end].iter().map(|s| s.to_string()).collect()
    }

    fn analyze_parent_structure(&self, content: &str, line_number: usize) -> Option<String> {
        if !self.analyze_structures {
            return None;
        }

        let lines: Vec<&str> = content.lines().collect();
        let mut current_line = if line_number > 0 { line_number - 1 } else { 0 };

        // Look backwards for struct, class, impl, or function definitions
        while current_line > 0 {
            let line = lines[current_line];
            if line.contains("struct ") || line.contains("class ") ||
               line.contains("impl ") || line.contains("fn ") {
                return Some(line.trim().to_string());
            }
            current_line -= 1;
        }

        None
    }

    fn find_child_references(&self, _content: &str, current_line: &str) -> Vec<String> {
        if !self.analyze_structures {
            return Vec::new();
        }

        let mut references = Vec::new();

        // Look for variable assignments, function calls, or method calls in the current line
        if let Some(captures) = regex::Regex::new(r"(\w+)\s*[=:]\s*").unwrap().captures(current_line) {
            if let Some(var_name) = captures.get(1) {
                references.push(var_name.as_str().to_string());
            }
        }

        // Look for function/method calls
        if let Some(captures) = regex::Regex::new(r"(\w+)\s*\(").unwrap().captures(current_line) {
            if let Some(func_name) = captures.get(1) {
                references.push(format!("{}()", func_name.as_str()));
            }
        }

        references
    }

    fn detect_obfuscation(&self, line: &str) -> String {
        let mut obfuscation_score = 0;

        // Check for base64-like patterns
        if regex::Regex::new(r"[A-Za-z0-9+/]{20,}={0,2}").unwrap().is_match(line) {
            obfuscation_score += 3;
        }

        // Check for hex patterns
        if regex::Regex::new(r"0x[0-9a-fA-F]{8,}").unwrap().is_match(line) {
            obfuscation_score += 2;
        }

        // Check for unusual character sequences
        if regex::Regex::new(r"[^a-zA-Z0-9\s]{5,}").unwrap().is_match(line) {
            obfuscation_score += 1;
        }

        match obfuscation_score {
            0 => "NONE".to_string(),
            1..=2 => "LOW".to_string(),
            3..=4 => "MEDIUM".to_string(),
            _ => "HIGH".to_string(),
        }
    }

    fn analyze_data_flow(&self, content: &str, line_number: usize) -> Option<String> {
        if !self.deep_scan {
            return None;
        }

        let lines: Vec<&str> = content.lines().collect();
        let current_line = lines.get(line_number.saturating_sub(1))?;

        // Simple data flow analysis - look for assignments and function calls
        if current_line.contains("=") && (current_line.contains("send") || current_line.contains("transmit")) {
            return Some("DATA_TRANSMISSION".to_string());
        }

        if current_line.contains("collect") || current_line.contains("gather") {
            return Some("DATA_COLLECTION".to_string());
        }

        if current_line.contains("encrypt") || current_line.contains("encode") {
            return Some("DATA_PROCESSING".to_string());
        }

        None
    }
}
