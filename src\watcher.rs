use crate::{scanner::FileScanner, Detection};
use colored::*;
use notify::{Config, Event, EventKind, RecommendedWatcher, RecursiveMode, Watcher};
use std::path::Path;
use std::sync::mpsc;
use std::time::Duration;
use tokio::time::sleep;

pub struct FileWatcher {
    scanner: FileScanner,
}

impl FileWatcher {
    pub fn new(scanner: FileScanner) -> Self {
        Self { scanner }
    }

    pub async fn watch(&self, path: &Path) -> Result<(), Box<dyn std::error::Error>> {
        let (tx, rx) = mpsc::channel();

        let mut watcher = RecommendedWatcher::new(
            move |res: Result<Event, notify::Error>| {
                if let Ok(event) = res {
                    let _ = tx.send(event);
                }
            },
            Config::default(),
        )?;

        watcher.watch(path, RecursiveMode::Recursive)?;

        println!("👁️  Watching for file changes in: {}", path.display().to_string().bright_cyan());
        println!("Press Ctrl+C to stop watching...\n");

        loop {
            // Check for file system events
            while let Ok(event) = rx.try_recv() {
                self.handle_file_event(event).await;
            }

            // Small delay to prevent busy waiting
            sleep(Duration::from_millis(100)).await;
        }
    }

    async fn handle_file_event(&self, event: Event) {
        match event.kind {
            EventKind::Create(_) | EventKind::Modify(_) => {
                for path in event.paths {
                    if path.is_file() {
                        // Check if file has target extension
                        if let Some(extension) = path.extension() {
                            if let Some(ext_str) = extension.to_str() {
                                let ext_lower = ext_str.to_lowercase();
                                if self.scanner.extensions.contains(&ext_lower) {
                                    println!("📝 File changed: {}", path.display().to_string().bright_yellow());
                                    
                                    match self.scanner.scan_file(&path).await {
                                        Ok(detections) => {
                                            if !detections.is_empty() {
                                                self.display_file_detections(&path, &detections);
                                            }
                                        }
                                        Err(e) => {
                                            eprintln!("❌ Error scanning {}: {}", path.display(), e);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            _ => {}
        }
    }

    fn display_file_detections(&self, file_path: &Path, detections: &[Detection]) {
        println!("\n🚨 {} detections in: {}", 
            detections.len().to_string().bright_red().bold(),
            file_path.display().to_string().bright_blue()
        );

        for detection in detections.iter().take(5) { // Show first 5 detections
            let severity_color = match detection.severity.as_str() {
                "HIGH" => "red",
                "MEDIUM" => "yellow", 
                "LOW" => "green",
                _ => "white",
            };

            println!("  {} Line {}: {}",
                detection.severity.color(severity_color).bold(),
                detection.line_number.to_string().bright_white(),
                detection.pattern_type.bright_cyan()
            );
            println!("    {}", detection.content.trim().bright_white());
        }

        if detections.len() > 5 {
            println!("    ... and {} more detections", (detections.len() - 5).to_string().bright_cyan());
        }
        println!();
    }
}
