{"rustc": 1842507548689473721, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 15221872889701672926, "path": 1912886073108449815, "deps": [[5820056977320921005, "anstream", false, 11558442621610543051], [9394696648929125047, "anstyle", false, 687770515873525904], [11166530783118767604, "strsim", false, 10527707293067370002], [11649982696571033535, "clap_lex", false, 15730643407080821366]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap_builder-36ec4ba9ec975143\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}