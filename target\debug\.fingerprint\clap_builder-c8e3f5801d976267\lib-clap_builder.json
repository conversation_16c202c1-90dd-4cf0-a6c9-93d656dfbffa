{"rustc": 1842507548689473721, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 15599109589607159429, "path": 1912886073108449815, "deps": [[5820056977320921005, "anstream", false, 7995914751603749064], [9394696648929125047, "anstyle", false, 9893404843306712314], [11166530783118767604, "strsim", false, 10370897055798005161], [11649982696571033535, "clap_lex", false, 4743606131067588851]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap_builder-c8e3f5801d976267\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}