{"rustc": 1842507548689473721, "features": "[\"crossbeam-channel\", \"default\", \"fsevent-sys\", \"macos_fsevent\"]", "declared_features": "[\"crossbeam-channel\", \"default\", \"fsevent-sys\", \"kqueue\", \"macos_fsevent\", \"macos_kqueue\", \"manual_tests\", \"mio\", \"serde\", \"timing_tests\"]", "target": 4487759779636071210, "profile": 2241668132362809309, "path": 12492707325745476319, "deps": [[1999565553139417705, "windows_sys", false, 11103731904356280040], [3869670940427635694, "filetime", false, 7905477889071878510], [4684437522915235464, "libc", false, 8722057067851681295], [5986029879202738730, "log", false, 5389931914812590872], [9727213718512686088, "crossbeam_channel", false, 11212261087006729863], [15622660310229662834, "walkdir", false, 10895263392906793495]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\notify-0894db6add1fd04f\\dep-lib-notify", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}