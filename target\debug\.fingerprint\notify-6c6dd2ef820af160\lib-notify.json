{"rustc": 1842507548689473721, "features": "[\"crossbeam-channel\", \"default\", \"fsevent-sys\", \"macos_fsevent\"]", "declared_features": "[\"crossbeam-channel\", \"default\", \"fsevent-sys\", \"kqueue\", \"macos_fsevent\", \"macos_kqueue\", \"manual_tests\", \"mio\", \"serde\", \"timing_tests\"]", "target": 4487759779636071210, "profile": 15657897354478470176, "path": 12492707325745476319, "deps": [[1999565553139417705, "windows_sys", false, 16806589433315514157], [3869670940427635694, "filetime", false, 3054639214474581094], [4684437522915235464, "libc", false, 12216959038647257109], [5986029879202738730, "log", false, 4691244668932318254], [9727213718512686088, "crossbeam_channel", false, 4849524943958589031], [15622660310229662834, "walkdir", false, 1817379337685309409]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\notify-6c6dd2ef820af160\\dep-lib-notify", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}