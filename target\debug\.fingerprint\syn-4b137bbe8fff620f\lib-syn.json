{"rustc": 1842507548689473721, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 17445931092983410937, "deps": [[1988483478007900009, "unicode_ident", false, 5963089085754783088], [3060637413840920116, "proc_macro2", false, 13131326433560521639], [17990358020177143287, "quote", false, 4692108879635327287]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-4b137bbe8fff620f\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}