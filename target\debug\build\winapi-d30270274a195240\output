cargo:rerun-if-changed=build.rs
cargo:rerun-if-env-changed=WINAPI_NO_BUNDLED_LIBRARIES
cargo:rerun-if-env-changed=WINAPI_STATIC_NOBUNDLE
cargo:rustc-cfg=feature="unknwnbase"
cargo:rustc-cfg=feature="objidlbase"
cargo:rustc-cfg=feature="ntstatus"
cargo:rustc-cfg=feature="ws2def"
cargo:rustc-cfg=feature="iptypes"
cargo:rustc-cfg=feature="processthreadsapi"
cargo:rustc-cfg=feature="udpmib"
cargo:rustc-cfg=feature="excpt"
cargo:rustc-cfg=feature="libloaderapi"
cargo:rustc-cfg=feature="evntcons"
cargo:rustc-cfg=feature="rpcndr"
cargo:rustc-cfg=feature="ntdef"
cargo:rustc-cfg=feature="reason"
cargo:rustc-cfg=feature="cfgmgr32"
cargo:rustc-cfg=feature="sspi"
cargo:rustc-cfg=feature="wincred"
cargo:rustc-cfg=feature="evntprov"
cargo:rustc-cfg=feature="winuser"
cargo:rustc-cfg=feature="limits"
cargo:rustc-cfg=feature="tcpmib"
cargo:rustc-cfg=feature="lsalookup"
cargo:rustc-cfg=feature="rpc"
cargo:rustc-cfg=feature="basetsd"
cargo:rustc-cfg=feature="vcruntime"
cargo:rustc-cfg=feature="ws2ipdef"
cargo:rustc-cfg=feature="ipifcons"
cargo:rustc-cfg=feature="tcpestats"
cargo:rustc-cfg=feature="wingdi"
cargo:rustc-cfg=feature="iprtrmib"
cargo:rustc-cfg=feature="guiddef"
cargo:rustc-cfg=feature="wtypesbase"
cargo:rustc-cfg=feature="ipmib"
cargo:rustc-cfg=feature="oaidl"
cargo:rustc-cfg=feature="propidl"
cargo:rustc-cfg=feature="vadefs"
cargo:rustc-cfg=feature="wtypes"
cargo:rustc-cfg=feature="ipexport"
cargo:rustc-cfg=feature="qos"
cargo:rustc-cfg=feature="nldef"
cargo:rustc-cfg=feature="ifmib"
cargo:rustc-cfg=feature="ktmtypes"
cargo:rustc-cfg=feature="devpropdef"
cargo:rustc-cfg=feature="corecrt"
cargo:rustc-cfg=feature="subauth"
cargo:rustc-cfg=feature="ntddndis"
cargo:rustc-cfg=feature="wmistr"
cargo:rustc-link-lib=dylib=advapi32
cargo:rustc-link-lib=dylib=cfgmgr32
cargo:rustc-link-lib=dylib=credui
cargo:rustc-link-lib=dylib=gdi32
cargo:rustc-link-lib=dylib=iphlpapi
cargo:rustc-link-lib=dylib=kernel32
cargo:rustc-link-lib=dylib=msimg32
cargo:rustc-link-lib=dylib=netapi32
cargo:rustc-link-lib=dylib=ole32
cargo:rustc-link-lib=dylib=oleaut32
cargo:rustc-link-lib=dylib=opengl32
cargo:rustc-link-lib=dylib=pdh
cargo:rustc-link-lib=dylib=powrprof
cargo:rustc-link-lib=dylib=psapi
cargo:rustc-link-lib=dylib=secur32
cargo:rustc-link-lib=dylib=shell32
cargo:rustc-link-lib=dylib=shlwapi
cargo:rustc-link-lib=dylib=synchronization
cargo:rustc-link-lib=dylib=user32
cargo:rustc-link-lib=dylib=winspool
cargo:rustc-link-lib=dylib=ws2_32
