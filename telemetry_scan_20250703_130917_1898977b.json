{"detections": [{"file_path": ".\\examples\\advanced_test.rs", "line_number": 15, "content": "    machine_ids: HashMap<String, String>,", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522957, "nanos_since_epoch": 642314000}, "context_lines": ["", "pub struct DeviceRegistry {", "    machine_ids: HashMap<String, String>,", "    hardware_fingerprints: Vec<HardwareFingerprint>,", "}", ""], "parent_structure": "pub struct DeviceRegistry {", "child_references": ["machine_ids"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 46, "content": "        let parent_id = self.generate_parent_machine_id();", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522957, "nanos_since_epoch": 666556000}, "context_lines": ["        ", "        // Complex ID generation with parent-child relationships", "        let parent_id = self.generate_parent_machine_id();", "        let child_ids = self.generate_child_device_ids(&parent_id);", "        ", "        // Data flow analysis - collection -> processing -> transmission"], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": ["parent_id", "generate_parent_machine_id()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 47, "content": "        let child_ids = self.generate_child_device_ids(&parent_id);", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522957, "nanos_since_epoch": 694547500}, "context_lines": ["        // Complex ID generation with parent-child relationships", "        let parent_id = self.generate_parent_machine_id();", "        let child_ids = self.generate_child_device_ids(&parent_id);", "        ", "        // Data flow analysis - collection -> processing -> transmission", "        let collected_data = self.collect_system_telemetry();"], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": ["child_ids", "generate_child_device_ids()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 55, "content": "    fn generate_parent_machine_id(&self) -> String {", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522957, "nanos_since_epoch": 729345600}, "context_lines": ["    }", "", "    fn generate_parent_machine_id(&self) -> String {", "        // Complex machine ID generation", "        let cpu_info = self.get_cpu_identification();", "        let mac_hash = self.hash_network_interfaces();"], "parent_structure": "fn generate_parent_machine_id(&self) -> String {", "child_references": ["generate_parent_machine_id()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 64, "content": "    fn generate_child_device_ids(&self, parent_id: &str) -> Vec<String> {", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522957, "nanos_since_epoch": 757008100}, "context_lines": ["    }", "", "    fn generate_child_device_ids(&self, parent_id: &str) -> Vec<String> {", "        let mut child_ids = Vec::new();", "        ", "        // Generate hierarchical device IDs"], "parent_structure": "fn generate_child_device_ids(&self, parent_id: &str) -> Vec<String> {", "child_references": ["parent_id", "generate_child_device_ids()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 223, "content": "            machine_ids: HashMap::new(),", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522957, "nanos_since_epoch": 794128400}, "context_lines": ["    fn new() -> Self {", "        Self {", "            machine_ids: HashMap::new(),", "            hardware_fingerprints: Vec::new(),", "        }", "    }"], "parent_structure": "fn new() -> Self {", "child_references": ["machine_ids", "new()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 52, "content": "        self.transmit_analytics_data(processed_data);", "pattern_type": "Data transmission to external servers (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522957, "nanos_since_epoch": 820743200}, "context_lines": ["        let collected_data = self.collect_system_telemetry();", "        let processed_data = self.process_telemetry_data(collected_data);", "        self.transmit_analytics_data(processed_data);", "    }", "", "    fn generate_parent_machine_id(&self) -> String {"], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": ["transmit_analytics_data()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 97, "content": "    async fn transmit_analytics_data(&self, data: ProcessedTelemetry) {", "pattern_type": "Data transmission to external servers (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522957, "nanos_since_epoch": 846065400}, "context_lines": ["    }", "", "    async fn transmit_analytics_data(&self, data: ProcessedTelemetry) {", "        // Steganographic data transmission", "        let hidden_payload = self.embed_in_image_data(&data);", "        "], "parent_structure": "async fn transmit_analytics_data(&self, data: ProcessedTelemetry) {", "child_references": ["data", "transmit_analytics_data()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 20, "content": "    cpu_serial: String,", "pattern_type": "CPU information collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522957, "nanos_since_epoch": 876538800}, "context_lines": ["", "pub struct HardwareFingerprint {", "    cpu_serial: String,", "    mac_addresses: Vec<String>,", "    disk_serials: Vec<String>,", "    system_uuid: String,"], "parent_structure": "pub struct HardwareFingerprint {", "child_references": ["cpu_serial"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 57, "content": "        let cpu_info = self.get_cpu_identification();", "pattern_type": "CPU information collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522957, "nanos_since_epoch": 908295000}, "context_lines": ["    fn generate_parent_machine_id(&self) -> String {", "        // Complex machine ID generation", "        let cpu_info = self.get_cpu_identification();", "        let mac_hash = self.hash_network_interfaces();", "        let disk_signature = self.get_disk_signatures();", "        "], "parent_structure": "fn generate_parent_machine_id(&self) -> String {", "child_references": ["cpu_info", "get_cpu_identification()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 61, "content": "        format!(\"{}:{}:{}\", cpu_info, mac_hash, disk_signature)", "pattern_type": "CPU information collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522957, "nanos_since_epoch": 940212500}, "context_lines": ["        let disk_signature = self.get_disk_signatures();", "        ", "        format!(\"{}:{}:{}\", cpu_info, mac_hash, disk_signature)", "    }", "", "    fn generate_child_device_ids(&self, parent_id: &str) -> Vec<String> {"], "parent_structure": "fn generate_parent_machine_id(&self) -> String {", "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 141, "content": "        \"cpu_serial_12345\".to_string()", "pattern_type": "CPU information collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522957, "nanos_since_epoch": 965196100}, "context_lines": ["    // System information gathering methods", "    fn get_cpu_identification(&self) -> String {", "        \"cpu_serial_12345\".to_string()", "    }", "", "    fn hash_network_interfaces(&self) -> String {"], "parent_structure": "fn get_cpu_identification(&self) -> String {", "child_references": ["to_string()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 1, "content": "// Advanced test file with complex telemetry and obfuscated patterns", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522957, "nanos_since_epoch": 993587700}, "context_lines": ["// Advanced test file with complex telemetry and obfuscated patterns", "", "use std::collections::HashMap;", "use serde_json;"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 8, "content": "pub struct TelemetryManager {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 16697500}, "context_lines": ["", "// Complex structure with parent-child relationships", "pub struct TelemetryManager {", "    pub device_registry: DeviceRegistry,", "    pub analytics_engine: AnalyticsEngine,", "    pub data_collector: DataCollector,"], "parent_structure": "pub struct TelemetryManager {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 10, "content": "    pub analytics_engine: AnalyticsEngine,", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 43423300}, "context_lines": ["pub struct TelemetryManager {", "    pub device_registry: DeviceRegistry,", "    pub analytics_engine: AnalyticsEngine,", "    pub data_collector: DataCollector,", "}", ""], "parent_structure": "pub struct TelemetryManager {", "child_references": ["analytics_engine"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 26, "content": "impl TelemetryManager {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 74970400}, "context_lines": ["}", "", "impl TelemetryManager {", "    pub fn new() -> Self {", "        let mut manager = Self {", "            device_registry: DeviceRegistry::new(),"], "parent_structure": "impl TelemetryManager {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 30, "content": "            analytics_engine: AnalyticsEngine::new(),", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 98918500}, "context_lines": ["        let mut manager = Self {", "            device_registry: DeviceRegistry::new(),", "            analytics_engine: AnalyticsEngine::new(),", "            data_collector: DataCollector::new(),", "        };", "        "], "parent_structure": "pub fn new() -> Self {", "child_references": ["analytics_engine", "new()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 35, "content": "        manager.initialize_obfuscated_tracking();", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 127537900}, "context_lines": ["        ", "        // Initialize with obfuscated data", "        manager.initialize_obfuscated_tracking();", "        manager", "    }", ""], "parent_structure": "pub fn new() -> Self {", "child_references": ["initialize_obfuscated_tracking()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 40, "content": "    fn initialize_obfuscated_tracking(&mut self) {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 155073800}, "context_lines": ["", "    // Obfuscated initialization", "    fn initialize_obfuscated_tracking(&mut self) {", "        // Base64 encoded tracking data", "        let encoded_config = \"dGVsZW1ldHJ5X2NvbmZpZ19kYXRh\";", "        let tracking_key = \"0x48656c6c6f576f726c64\";"], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": ["initialize_obfuscated_tracking()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 41, "content": "        // Base64 encoded tracking data", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 180848500}, "context_lines": ["    // Obfuscated initialization", "    fn initialize_obfuscated_tracking(&mut self) {", "        // Base64 encoded tracking data", "        let encoded_config = \"dGVsZW1ldHJ5X2NvbmZpZ19kYXRh\";", "        let tracking_key = \"0x48656c6c6f576f726c64\";", "        "], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_PROCESSING"}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 43, "content": "        let tracking_key = \"0x48656c6c6f576f726c64\";", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 205216100}, "context_lines": ["        // Base64 encoded tracking data", "        let encoded_config = \"dGVsZW1ldHJ5X2NvbmZpZ19kYXRh\";", "        let tracking_key = \"0x48656c6c6f576f726c64\";", "        ", "        // Complex ID generation with parent-child relationships", "        let parent_id = self.generate_parent_machine_id();"], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": ["tracking_key"], "obfuscation_level": "HIGH", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 50, "content": "        let collected_data = self.collect_system_telemetry();", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 233094800}, "context_lines": ["        ", "        // Data flow analysis - collection -> processing -> transmission", "        let collected_data = self.collect_system_telemetry();", "        let processed_data = self.process_telemetry_data(collected_data);", "        self.transmit_analytics_data(processed_data);", "    }"], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": ["collected_data", "collect_system_telemetry()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 51, "content": "        let processed_data = self.process_telemetry_data(collected_data);", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 260929000}, "context_lines": ["        // Data flow analysis - collection -> processing -> transmission", "        let collected_data = self.collect_system_telemetry();", "        let processed_data = self.process_telemetry_data(collected_data);", "        self.transmit_analytics_data(processed_data);", "    }", ""], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": ["processed_data", "process_telemetry_data()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 52, "content": "        self.transmit_analytics_data(processed_data);", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 288711700}, "context_lines": ["        let collected_data = self.collect_system_telemetry();", "        let processed_data = self.process_telemetry_data(collected_data);", "        self.transmit_analytics_data(processed_data);", "    }", "", "    fn generate_parent_machine_id(&self) -> String {"], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": ["transmit_analytics_data()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 76, "content": "    fn collect_system_telemetry(&self) -> TelemetryData {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 312477600}, "context_lines": ["    }", "", "    fn collect_system_telemetry(&self) -> TelemetryData {", "        TelemetryData {", "            user_behavior: self.track_user_interactions(),", "            system_metrics: self.gather_system_metrics(),"], "parent_structure": "fn collect_system_telemetry(&self) -> TelemetryData {", "child_references": ["collect_system_telemetry()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 77, "content": "        TelemetryData {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 339773100}, "context_lines": ["", "    fn collect_system_telemetry(&self) -> TelemetryData {", "        TelemetryData {", "            user_behavior: self.track_user_interactions(),", "            system_metrics: self.gather_system_metrics(),", "            network_activity: self.monitor_network_traffic(),"], "parent_structure": "fn collect_system_telemetry(&self) -> TelemetryData {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 79, "content": "            system_metrics: self.gather_system_metrics(),", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 362485700}, "context_lines": ["        TelemetryData {", "            user_behavior: self.track_user_interactions(),", "            system_metrics: self.gather_system_metrics(),", "            network_activity: self.monitor_network_traffic(),", "            application_usage: self.track_application_usage(),", "        }"], "parent_structure": "fn collect_system_telemetry(&self) -> TelemetryData {", "child_references": ["system_metrics", "gather_system_metrics()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 85, "content": "    fn process_telemetry_data(&self, data: TelemetryData) -> ProcessedTelemetry {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 389578500}, "context_lines": ["    }", "", "    fn process_telemetry_data(&self, data: TelemetryData) -> ProcessedTelemetry {", "        // Encrypt and obfuscate telemetry data", "        let encrypted_payload = self.encrypt_telemetry_payload(&data);", "        let obfuscated_metadata = self.obfuscate_metadata(&data);"], "parent_structure": "fn process_telemetry_data(&self, data: TelemetryData) -> ProcessedTelemetry {", "child_references": ["data", "process_telemetry_data()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 86, "content": "        // Encrypt and obfuscate telemetry data", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 415973100}, "context_lines": ["", "    fn process_telemetry_data(&self, data: TelemetryData) -> ProcessedTelemetry {", "        // Encrypt and obfuscate telemetry data", "        let encrypted_payload = self.encrypt_telemetry_payload(&data);", "        let obfuscated_metadata = self.obfuscate_metadata(&data);", "        "], "parent_structure": "fn process_telemetry_data(&self, data: TelemetryData) -> ProcessedTelemetry {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 87, "content": "        let encrypted_payload = self.encrypt_telemetry_payload(&data);", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 441915600}, "context_lines": ["    fn process_telemetry_data(&self, data: TelemetryData) -> ProcessedTelemetry {", "        // Encrypt and obfuscate telemetry data", "        let encrypted_payload = self.encrypt_telemetry_payload(&data);", "        let obfuscated_metadata = self.obfuscate_metadata(&data);", "        ", "        ProcessedTelemetry {"], "parent_structure": "fn process_telemetry_data(&self, data: TelemetryData) -> ProcessedTelemetry {", "child_references": ["encrypted_payload", "encrypt_telemetry_payload()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_PROCESSING"}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 90, "content": "        ProcessedTelemetry {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 473669400}, "context_lines": ["        let obfuscated_metadata = self.obfuscate_metadata(&data);", "        ", "        ProcessedTelemetry {", "            encrypted_data: encrypted_payload,", "            metadata: obfuscated_metadata,", "            transmission_key: self.generate_transmission_key(),"], "parent_structure": "fn process_telemetry_data(&self, data: TelemetryData) -> ProcessedTelemetry {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 97, "content": "    async fn transmit_analytics_data(&self, data: ProcessedTelemetry) {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 503077400}, "context_lines": ["    }", "", "    async fn transmit_analytics_data(&self, data: ProcessedTelemetry) {", "        // Steganographic data transmission", "        let hidden_payload = self.embed_in_image_data(&data);", "        "], "parent_structure": "async fn transmit_analytics_data(&self, data: ProcessedTelemetry) {", "child_references": ["data", "transmit_analytics_data()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 113, "content": "    fn encrypt_telemetry_payload(&self, data: &TelemetryData) -> Vec<u8> {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 537630200}, "context_lines": ["", "    // Advanced obfuscation methods", "    fn encrypt_telemetry_payload(&self, data: &TelemetryData) -> Vec<u8> {", "        // Placeholder for complex encryption", "        vec![0x41, 0x42, 0x43, 0x44] // \"ABCD\" in hex", "    }"], "parent_structure": "fn encrypt_telemetry_payload(&self, data: &TelemetryData) -> Vec<u8> {", "child_references": ["data", "encrypt_telemetry_payload()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_PROCESSING"}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 118, "content": "    fn obfuscate_metadata(&self, data: &TelemetryData) -> String {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 590571200}, "context_lines": ["    }", "", "    fn obfuscate_metadata(&self, data: &TelemetryData) -> String {", "        // Base64 obfuscation of metadata", "        base64::encode(format!(\"metadata:{:?}\", data))", "    }"], "parent_structure": "fn obfuscate_metadata(&self, data: &TelemetryData) -> String {", "child_references": ["data", "obfuscate_metadata()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 123, "content": "    fn embed_in_image_data(&self, data: &ProcessedTelemetry) -> Vec<u8> {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 618846700}, "context_lines": ["    }", "", "    fn embed_in_image_data(&self, data: &ProcessedTelemetry) -> Vec<u8> {", "        // Steganographic embedding", "        vec![0xFF, 0xD8, 0xFF, 0xE0] // JPEG header with hidden data", "    }"], "parent_structure": "fn embed_in_image_data(&self, data: &ProcessedTelemetry) -> Vec<u8> {", "child_references": ["data", "embed_in_image_data()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 156, "content": "    fn gather_system_metrics(&self) -> SystemMetrics {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 647549500}, "context_lines": ["    }", "", "    fn gather_system_metrics(&self) -> SystemMetrics {", "        SystemMetrics::default()", "    }", ""], "parent_structure": "fn gather_system_metrics(&self) -> SystemMetrics {", "child_references": ["gather_system_metrics()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 157, "content": "        SystemMetrics::default()", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 673216900}, "context_lines": ["", "    fn gather_system_metrics(&self) -> SystemMetrics {", "        SystemMetrics::default()", "    }", "", "    fn monitor_network_traffic(&self) -> NetworkActivity {"], "parent_structure": "fn gather_system_metrics(&self) -> SystemMetrics {", "child_references": ["SystemMetrics", "default()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 189, "content": "pub struct TelemetryData {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 700222500}, "context_lines": ["// Supporting structures", "#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)]", "pub struct TelemetryData {", "    user_behavior: <PERSON>r<PERSON><PERSON><PERSON><PERSON>,", "    system_metrics: SystemMetrics,", "    network_activity: NetworkActivity,"], "parent_structure": "pub struct TelemetryData {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 191, "content": "    system_metrics: SystemMetrics,", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 729908400}, "context_lines": ["pub struct TelemetryData {", "    user_behavior: <PERSON>r<PERSON><PERSON><PERSON><PERSON>,", "    system_metrics: SystemMetrics,", "    network_activity: NetworkActivity,", "    application_usage: ApplicationUsage,", "}"], "parent_structure": "pub struct TelemetryData {", "child_references": ["system_metrics"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 200, "content": "pub struct SystemMetrics;", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 761481400}, "context_lines": ["", "#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)]", "pub struct SystemMetrics;", "", "#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)]", "pub struct NetworkActivity;"], "parent_structure": "pub struct SystemMetrics;", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 208, "content": "pub struct ProcessedTelemetry {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 785747000}, "context_lines": ["pub struct ApplicationUsage;", "", "pub struct ProcessedTelemetry {", "    encrypted_data: Vec<u8>,", "    metadata: String,", "    transmission_key: String,"], "parent_structure": "pub struct ProcessedTelemetry {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 229, "content": "pub struct AnalyticsEngine;", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 813853300}, "context_lines": ["}", "", "pub struct AnalyticsEngine;", "impl AnalyticsEngine {", "    fn new() -> Self { Self }", "}"], "parent_structure": "pub struct AnalyticsEngine;", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 230, "content": "impl AnalyticsEngine {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 839558700}, "context_lines": ["", "pub struct AnalyticsEngine;", "impl AnalyticsEngine {", "    fn new() -> Self { Self }", "}", ""], "parent_structure": "impl AnalyticsEngine {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 35, "content": "        manager.initialize_obfuscated_tracking();", "pattern_type": "Configuration file access (FILESYSTEM)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 862495600}, "context_lines": ["        ", "        // Initialize with obfuscated data", "        manager.initialize_obfuscated_tracking();", "        manager", "    }", ""], "parent_structure": "pub fn new() -> Self {", "child_references": ["initialize_obfuscated_tracking()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 46, "content": "        let parent_id = self.generate_parent_machine_id();", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 889020000}, "context_lines": ["        ", "        // Complex ID generation with parent-child relationships", "        let parent_id = self.generate_parent_machine_id();", "        let child_ids = self.generate_child_device_ids(&parent_id);", "        ", "        // Data flow analysis - collection -> processing -> transmission"], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": ["parent_id", "generate_parent_machine_id()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 47, "content": "        let child_ids = self.generate_child_device_ids(&parent_id);", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 919412700}, "context_lines": ["        // Complex ID generation with parent-child relationships", "        let parent_id = self.generate_parent_machine_id();", "        let child_ids = self.generate_child_device_ids(&parent_id);", "        ", "        // Data flow analysis - collection -> processing -> transmission", "        let collected_data = self.collect_system_telemetry();"], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": ["child_ids", "generate_child_device_ids()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 55, "content": "    fn generate_parent_machine_id(&self) -> String {", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 949908500}, "context_lines": ["    }", "", "    fn generate_parent_machine_id(&self) -> String {", "        // Complex machine ID generation", "        let cpu_info = self.get_cpu_identification();", "        let mac_hash = self.hash_network_interfaces();"], "parent_structure": "fn generate_parent_machine_id(&self) -> String {", "child_references": ["generate_parent_machine_id()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 64, "content": "    fn generate_child_device_ids(&self, parent_id: &str) -> Vec<String> {", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522958, "nanos_since_epoch": 977713200}, "context_lines": ["    }", "", "    fn generate_child_device_ids(&self, parent_id: &str) -> Vec<String> {", "        let mut child_ids = Vec::new();", "        ", "        // Generate hierarchical device IDs"], "parent_structure": "fn generate_child_device_ids(&self, parent_id: &str) -> Vec<String> {", "child_references": ["parent_id", "generate_child_device_ids()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 65, "content": "        let mut child_ids = Vec::new();", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 7377300}, "context_lines": ["", "    fn generate_child_device_ids(&self, parent_id: &str) -> Vec<String> {", "        let mut child_ids = Vec::new();", "        ", "        // Generate hierarchical device IDs", "        for i in 0..5 {"], "parent_structure": "fn generate_child_device_ids(&self, parent_id: &str) -> Vec<String> {", "child_references": ["child_ids", "new()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 69, "content": "            let child_id = format!(\"{}:child_{}\", parent_id, i);", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 35034300}, "context_lines": ["        // Generate hierarchical device IDs", "        for i in 0..5 {", "            let child_id = format!(\"{}:child_{}\", parent_id, i);", "            child_ids.push(child_id);", "        }", "        "], "parent_structure": "fn generate_child_device_ids(&self, parent_id: &str) -> Vec<String> {", "child_references": ["child_id"], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 70, "content": "            child_ids.push(child_id);", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 64420000}, "context_lines": ["        for i in 0..5 {", "            let child_id = format!(\"{}:child_{}\", parent_id, i);", "            child_ids.push(child_id);", "        }", "        ", "        child_ids"], "parent_structure": "fn generate_child_device_ids(&self, parent_id: &str) -> Vec<String> {", "child_references": ["push()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 73, "content": "        child_ids", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 91533900}, "context_lines": ["        }", "        ", "        child_ids", "    }", "", "    fn collect_system_telemetry(&self) -> TelemetryData {"], "parent_structure": "fn generate_child_device_ids(&self, parent_id: &str) -> Vec<String> {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 22, "content": "    disk_serials: Vec<String>,", "pattern_type": "Disk/Drive serial number collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 114315100}, "context_lines": ["    cpu_serial: String,", "    mac_addresses: Vec<String>,", "    disk_serials: Vec<String>,", "    system_uuid: String,", "}", ""], "parent_structure": "pub struct HardwareFingerprint {", "child_references": ["disk_serials"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 21, "content": "    mac_addresses: Vec<String>,", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 140543400}, "context_lines": ["pub struct HardwareFingerprint {", "    cpu_serial: String,", "    mac_addresses: Vec<String>,", "    disk_serials: Vec<String>,", "    system_uuid: String,", "}"], "parent_structure": "pub struct HardwareFingerprint {", "child_references": ["mac_addresses"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 58, "content": "        let mac_hash = self.hash_network_interfaces();", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 164746800}, "context_lines": ["        // Complex machine ID generation", "        let cpu_info = self.get_cpu_identification();", "        let mac_hash = self.hash_network_interfaces();", "        let disk_signature = self.get_disk_signatures();", "        ", "        format!(\"{}:{}:{}\", cpu_info, mac_hash, disk_signature)"], "parent_structure": "fn generate_parent_machine_id(&self) -> String {", "child_references": ["mac_hash", "hash_network_interfaces()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 144, "content": "    fn hash_network_interfaces(&self) -> String {", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 193433800}, "context_lines": ["    }", "", "    fn hash_network_interfaces(&self) -> String {", "        \"mac_hash_67890\".to_string()", "    }", ""], "parent_structure": "fn hash_network_interfaces(&self) -> String {", "child_references": ["hash_network_interfaces()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 78, "content": "            user_behavior: self.track_user_interactions(),", "pattern_type": "User behavior tracking (TELEMETRY)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 219689100}, "context_lines": ["    fn collect_system_telemetry(&self) -> TelemetryData {", "        TelemetryData {", "            user_behavior: self.track_user_interactions(),", "            system_metrics: self.gather_system_metrics(),", "            network_activity: self.monitor_network_traffic(),", "            application_usage: self.track_application_usage(),"], "parent_structure": "fn collect_system_telemetry(&self) -> TelemetryData {", "child_references": ["user_behavior", "track_user_interactions()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 152, "content": "    fn track_user_interactions(&self) -> UserBehavior {", "pattern_type": "User behavior tracking (TELEMETRY)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 251020600}, "context_lines": ["    }", "", "    fn track_user_interactions(&self) -> UserBehavior {", "        UserBehavior::default()", "    }", ""], "parent_structure": "fn track_user_interactions(&self) -> UserBehavior {", "child_references": ["track_user_interactions()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 153, "content": "        UserBehavior::default()", "pattern_type": "User behavior tracking (TELEMETRY)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 283515300}, "context_lines": ["", "    fn track_user_interactions(&self) -> UserBehavior {", "        UserBehavior::default()", "    }", "", "    fn gather_system_metrics(&self) -> SystemMetrics {"], "parent_structure": "fn track_user_interactions(&self) -> UserBehavior {", "child_references": ["UserBehavior", "default()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 190, "content": "    user_behavior: <PERSON>r<PERSON><PERSON><PERSON><PERSON>,", "pattern_type": "User behavior tracking (TELEMETRY)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 322269600}, "context_lines": ["#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)]", "pub struct TelemetryData {", "    user_behavior: <PERSON>r<PERSON><PERSON><PERSON><PERSON>,", "    system_metrics: SystemMetrics,", "    network_activity: NetworkActivity,", "    application_usage: ApplicationUsage,"], "parent_structure": "pub struct TelemetryData {", "child_references": ["user_behavior"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 197, "content": "pub struct UserBehavior;", "pattern_type": "User behavior tracking (TELEMETRY)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 351353700}, "context_lines": ["", "#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)]", "pub struct UserBehavior;", "", "#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)]", "pub struct SystemMetrics;"], "parent_structure": "pub struct UserBehavior;", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 9, "content": "    pub device_registry: DeviceRegistry,", "pattern_type": "Windows Registry access (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 378919500}, "context_lines": ["// Complex structure with parent-child relationships", "pub struct TelemetryManager {", "    pub device_registry: DeviceRegistry,", "    pub analytics_engine: AnalyticsEngine,", "    pub data_collector: DataCollector,", "}"], "parent_structure": "pub struct TelemetryManager {", "child_references": ["device_registry"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 14, "content": "pub struct DeviceRegistry {", "pattern_type": "Windows Registry access (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 408541000}, "context_lines": ["}", "", "pub struct DeviceRegistry {", "    machine_ids: HashMap<String, String>,", "    hardware_fingerprints: Vec<HardwareFingerprint>,", "}"], "parent_structure": "pub struct DeviceRegistry {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 29, "content": "            device_registry: DeviceRegistry::new(),", "pattern_type": "Windows Registry access (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 456207900}, "context_lines": ["    pub fn new() -> Self {", "        let mut manager = Self {", "            device_registry: DeviceRegistry::new(),", "            analytics_engine: AnalyticsEngine::new(),", "            data_collector: DataCollector::new(),", "        };"], "parent_structure": "pub fn new() -> Self {", "child_references": ["device_registry", "new()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 220, "content": "impl DeviceRegistry {", "pattern_type": "Windows Registry access (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 489436300}, "context_lines": ["}", "", "impl DeviceRegistry {", "    fn new() -> Self {", "        Self {", "            machine_ids: HashMap::new(),"], "parent_structure": "impl DeviceRegistry {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 1, "content": "// Advanced test file with complex telemetry and obfuscated patterns", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 516159600}, "context_lines": ["// Advanced test file with complex telemetry and obfuscated patterns", "", "use std::collections::HashMap;", "use serde_json;"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 8, "content": "pub struct TelemetryManager {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 543978600}, "context_lines": ["", "// Complex structure with parent-child relationships", "pub struct TelemetryManager {", "    pub device_registry: DeviceRegistry,", "    pub analytics_engine: AnalyticsEngine,", "    pub data_collector: DataCollector,"], "parent_structure": "pub struct TelemetryManager {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 10, "content": "    pub analytics_engine: AnalyticsEngine,", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 573743800}, "context_lines": ["pub struct TelemetryManager {", "    pub device_registry: DeviceRegistry,", "    pub analytics_engine: AnalyticsEngine,", "    pub data_collector: DataCollector,", "}", ""], "parent_structure": "pub struct TelemetryManager {", "child_references": ["analytics_engine"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 26, "content": "impl TelemetryManager {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 607942900}, "context_lines": ["}", "", "impl TelemetryManager {", "    pub fn new() -> Self {", "        let mut manager = Self {", "            device_registry: DeviceRegistry::new(),"], "parent_structure": "impl TelemetryManager {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 30, "content": "            analytics_engine: AnalyticsEngine::new(),", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 642716500}, "context_lines": ["        let mut manager = Self {", "            device_registry: DeviceRegistry::new(),", "            analytics_engine: AnalyticsEngine::new(),", "            data_collector: DataCollector::new(),", "        };", "        "], "parent_structure": "pub fn new() -> Self {", "child_references": ["analytics_engine", "new()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 35, "content": "        manager.initialize_obfuscated_tracking();", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 673660900}, "context_lines": ["        ", "        // Initialize with obfuscated data", "        manager.initialize_obfuscated_tracking();", "        manager", "    }", ""], "parent_structure": "pub fn new() -> Self {", "child_references": ["initialize_obfuscated_tracking()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 40, "content": "    fn initialize_obfuscated_tracking(&mut self) {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 704311800}, "context_lines": ["", "    // Obfuscated initialization", "    fn initialize_obfuscated_tracking(&mut self) {", "        // Base64 encoded tracking data", "        let encoded_config = \"dGVsZW1ldHJ5X2NvbmZpZ19kYXRh\";", "        let tracking_key = \"0x48656c6c6f576f726c64\";"], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": ["initialize_obfuscated_tracking()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 41, "content": "        // Base64 encoded tracking data", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 734098700}, "context_lines": ["    // Obfuscated initialization", "    fn initialize_obfuscated_tracking(&mut self) {", "        // Base64 encoded tracking data", "        let encoded_config = \"dGVsZW1ldHJ5X2NvbmZpZ19kYXRh\";", "        let tracking_key = \"0x48656c6c6f576f726c64\";", "        "], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_PROCESSING"}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 43, "content": "        let tracking_key = \"0x48656c6c6f576f726c64\";", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 761363500}, "context_lines": ["        // Base64 encoded tracking data", "        let encoded_config = \"dGVsZW1ldHJ5X2NvbmZpZ19kYXRh\";", "        let tracking_key = \"0x48656c6c6f576f726c64\";", "        ", "        // Complex ID generation with parent-child relationships", "        let parent_id = self.generate_parent_machine_id();"], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": ["tracking_key"], "obfuscation_level": "HIGH", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 50, "content": "        let collected_data = self.collect_system_telemetry();", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 792560000}, "context_lines": ["        ", "        // Data flow analysis - collection -> processing -> transmission", "        let collected_data = self.collect_system_telemetry();", "        let processed_data = self.process_telemetry_data(collected_data);", "        self.transmit_analytics_data(processed_data);", "    }"], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": ["collected_data", "collect_system_telemetry()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 51, "content": "        let processed_data = self.process_telemetry_data(collected_data);", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 823668600}, "context_lines": ["        // Data flow analysis - collection -> processing -> transmission", "        let collected_data = self.collect_system_telemetry();", "        let processed_data = self.process_telemetry_data(collected_data);", "        self.transmit_analytics_data(processed_data);", "    }", ""], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": ["processed_data", "process_telemetry_data()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 52, "content": "        self.transmit_analytics_data(processed_data);", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 853506800}, "context_lines": ["        let collected_data = self.collect_system_telemetry();", "        let processed_data = self.process_telemetry_data(collected_data);", "        self.transmit_analytics_data(processed_data);", "    }", "", "    fn generate_parent_machine_id(&self) -> String {"], "parent_structure": "fn initialize_obfuscated_tracking(&mut self) {", "child_references": ["transmit_analytics_data()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 76, "content": "    fn collect_system_telemetry(&self) -> TelemetryData {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 879776200}, "context_lines": ["    }", "", "    fn collect_system_telemetry(&self) -> TelemetryData {", "        TelemetryData {", "            user_behavior: self.track_user_interactions(),", "            system_metrics: self.gather_system_metrics(),"], "parent_structure": "fn collect_system_telemetry(&self) -> TelemetryData {", "child_references": ["collect_system_telemetry()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 77, "content": "        TelemetryData {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 911804200}, "context_lines": ["", "    fn collect_system_telemetry(&self) -> TelemetryData {", "        TelemetryData {", "            user_behavior: self.track_user_interactions(),", "            system_metrics: self.gather_system_metrics(),", "            network_activity: self.monitor_network_traffic(),"], "parent_structure": "fn collect_system_telemetry(&self) -> TelemetryData {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 85, "content": "    fn process_telemetry_data(&self, data: TelemetryData) -> ProcessedTelemetry {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 941356100}, "context_lines": ["    }", "", "    fn process_telemetry_data(&self, data: TelemetryData) -> ProcessedTelemetry {", "        // Encrypt and obfuscate telemetry data", "        let encrypted_payload = self.encrypt_telemetry_payload(&data);", "        let obfuscated_metadata = self.obfuscate_metadata(&data);"], "parent_structure": "fn process_telemetry_data(&self, data: TelemetryData) -> ProcessedTelemetry {", "child_references": ["data", "process_telemetry_data()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 86, "content": "        // Encrypt and obfuscate telemetry data", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 973252400}, "context_lines": ["", "    fn process_telemetry_data(&self, data: TelemetryData) -> ProcessedTelemetry {", "        // Encrypt and obfuscate telemetry data", "        let encrypted_payload = self.encrypt_telemetry_payload(&data);", "        let obfuscated_metadata = self.obfuscate_metadata(&data);", "        "], "parent_structure": "fn process_telemetry_data(&self, data: TelemetryData) -> ProcessedTelemetry {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 87, "content": "        let encrypted_payload = self.encrypt_telemetry_payload(&data);", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522959, "nanos_since_epoch": 998381600}, "context_lines": ["    fn process_telemetry_data(&self, data: TelemetryData) -> ProcessedTelemetry {", "        // Encrypt and obfuscate telemetry data", "        let encrypted_payload = self.encrypt_telemetry_payload(&data);", "        let obfuscated_metadata = self.obfuscate_metadata(&data);", "        ", "        ProcessedTelemetry {"], "parent_structure": "fn process_telemetry_data(&self, data: TelemetryData) -> ProcessedTelemetry {", "child_references": ["encrypted_payload", "encrypt_telemetry_payload()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_PROCESSING"}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 90, "content": "        ProcessedTelemetry {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 30373500}, "context_lines": ["        let obfuscated_metadata = self.obfuscate_metadata(&data);", "        ", "        ProcessedTelemetry {", "            encrypted_data: encrypted_payload,", "            metadata: obfuscated_metadata,", "            transmission_key: self.generate_transmission_key(),"], "parent_structure": "fn process_telemetry_data(&self, data: TelemetryData) -> ProcessedTelemetry {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 97, "content": "    async fn transmit_analytics_data(&self, data: ProcessedTelemetry) {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 56833800}, "context_lines": ["    }", "", "    async fn transmit_analytics_data(&self, data: ProcessedTelemetry) {", "        // Steganographic data transmission", "        let hidden_payload = self.embed_in_image_data(&data);", "        "], "parent_structure": "async fn transmit_analytics_data(&self, data: ProcessedTelemetry) {", "child_references": ["data", "transmit_analytics_data()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 113, "content": "    fn encrypt_telemetry_payload(&self, data: &TelemetryData) -> Vec<u8> {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 88898700}, "context_lines": ["", "    // Advanced obfuscation methods", "    fn encrypt_telemetry_payload(&self, data: &TelemetryData) -> Vec<u8> {", "        // Placeholder for complex encryption", "        vec![0x41, 0x42, 0x43, 0x44] // \"ABCD\" in hex", "    }"], "parent_structure": "fn encrypt_telemetry_payload(&self, data: &TelemetryData) -> Vec<u8> {", "child_references": ["data", "encrypt_telemetry_payload()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_PROCESSING"}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 118, "content": "    fn obfuscate_metadata(&self, data: &TelemetryData) -> String {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 122291300}, "context_lines": ["    }", "", "    fn obfuscate_metadata(&self, data: &TelemetryData) -> String {", "        // Base64 obfuscation of metadata", "        base64::encode(format!(\"metadata:{:?}\", data))", "    }"], "parent_structure": "fn obfuscate_metadata(&self, data: &TelemetryData) -> String {", "child_references": ["data", "obfuscate_metadata()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 123, "content": "    fn embed_in_image_data(&self, data: &ProcessedTelemetry) -> Vec<u8> {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 150601200}, "context_lines": ["    }", "", "    fn embed_in_image_data(&self, data: &ProcessedTelemetry) -> Vec<u8> {", "        // Steganographic embedding", "        vec![0xFF, 0xD8, 0xFF, 0xE0] // JPEG header with hidden data", "    }"], "parent_structure": "fn embed_in_image_data(&self, data: &ProcessedTelemetry) -> Vec<u8> {", "child_references": ["data", "embed_in_image_data()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 189, "content": "pub struct TelemetryData {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 179659000}, "context_lines": ["// Supporting structures", "#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)]", "pub struct TelemetryData {", "    user_behavior: <PERSON>r<PERSON><PERSON><PERSON><PERSON>,", "    system_metrics: SystemMetrics,", "    network_activity: NetworkActivity,"], "parent_structure": "pub struct TelemetryData {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 208, "content": "pub struct ProcessedTelemetry {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 204335100}, "context_lines": ["pub struct ApplicationUsage;", "", "pub struct ProcessedTelemetry {", "    encrypted_data: Vec<u8>,", "    metadata: String,", "    transmission_key: String,"], "parent_structure": "pub struct ProcessedTelemetry {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 229, "content": "pub struct AnalyticsEngine;", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 227405900}, "context_lines": ["}", "", "pub struct AnalyticsEngine;", "impl AnalyticsEngine {", "    fn new() -> Self { Self }", "}"], "parent_structure": "pub struct AnalyticsEngine;", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 230, "content": "impl AnalyticsEngine {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 251026100}, "context_lines": ["", "pub struct AnalyticsEngine;", "impl AnalyticsEngine {", "    fn new() -> Self { Self }", "}", ""], "parent_structure": "impl AnalyticsEngine {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 8, "content": "pub struct TelemetryManager {", "pattern_type": "Complex data structures for tracking (STRUCTURE)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 274765500}, "context_lines": ["", "// Complex structure with parent-child relationships", "pub struct TelemetryManager {", "    pub device_registry: DeviceRegistry,", "    pub analytics_engine: AnalyticsEngine,", "    pub data_collector: DataCollector,"], "parent_structure": "pub struct TelemetryManager {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 14, "content": "pub struct DeviceRegistry {", "pattern_type": "Complex data structures for tracking (STRUCTURE)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 297529700}, "context_lines": ["}", "", "pub struct DeviceRegistry {", "    machine_ids: HashMap<String, String>,", "    hardware_fingerprints: Vec<HardwareFingerprint>,", "}"], "parent_structure": "pub struct DeviceRegistry {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 189, "content": "pub struct TelemetryData {", "pattern_type": "Complex data structures for tracking (STRUCTURE)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 321646600}, "context_lines": ["// Supporting structures", "#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)]", "pub struct TelemetryData {", "    user_behavior: <PERSON>r<PERSON><PERSON><PERSON><PERSON>,", "    system_metrics: SystemMetrics,", "    network_activity: NetworkActivity,"], "parent_structure": "pub struct TelemetryData {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 208, "content": "pub struct ProcessedTelemetry {", "pattern_type": "Complex data structures for tracking (STRUCTURE)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 344230800}, "context_lines": ["pub struct ApplicationUsage;", "", "pub struct ProcessedTelemetry {", "    encrypted_data: Vec<u8>,", "    metadata: String,", "    transmission_key: String,"], "parent_structure": "pub struct ProcessedTelemetry {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\advanced_test.rs", "line_number": 229, "content": "pub struct AnalyticsEngine;", "pattern_type": "Complex data structures for tracking (STRUCTURE)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 367306400}, "context_lines": ["}", "", "pub struct AnalyticsEngine;", "impl AnalyticsEngine {", "    fn new() -> Self { Self }", "}"], "parent_structure": "pub struct AnalyticsEngine;", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 9, "content": "    let machine_id = machine_uid::get().unwrap();", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 396657300}, "context_lines": ["fn collect_machine_info() {", "    // Machine ID generation", "    let machine_id = machine_uid::get().unwrap();", "    let device_id = Uuid::new_v4();", "    ", "    // System information collection"], "parent_structure": "fn collect_machine_info() {", "child_references": ["machine_id", "get()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 10, "content": "    let device_id = Uuid::new_v4();", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 427871200}, "context_lines": ["    // Machine ID generation", "    let machine_id = machine_uid::get().unwrap();", "    let device_id = Uuid::new_v4();", "    ", "    // System information collection", "    let mut system = System::new_all();"], "parent_structure": "fn collect_machine_info() {", "child_references": ["device_id", "new_v4()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 16, "content": "    let cpu_info = system.processors();", "pattern_type": "CPU information collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 458503700}, "context_lines": ["    system.refresh_all();", "    ", "    let cpu_info = system.processors();", "    let mac_address = get_mac_address();", "    ", "    // Telemetry collection"], "parent_structure": "fn collect_machine_info() {", "child_references": ["cpu_info", "processors()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 55, "content": "fn crash_reporter(error: &str) {", "pattern_type": "Crash/Error reporting (TELEMETRY)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 488737200}, "context_lines": ["}", "", "fn crash_reporter(error: &str) {", "    // Crash reporting", "    let crash_data = format!(\"Error: {}\", error);", "    // This would typically send to a crash reporting service"], "parent_structure": "fn crash_reporter(error: &str) {", "child_references": ["error", "crash_reporter()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 1, "content": "// Example file with telemetry and machine ID patterns for testing", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 517321500}, "context_lines": ["// Example file with telemetry and machine ID patterns for testing", "", "use sysinfo::{System, SystemExt, ProcessorExt};", "use uuid::Uuid;"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 19, "content": "    // Telemetry collection", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 541373800}, "context_lines": ["    let mac_address = get_mac_address();", "    ", "    // Telemetry collection", "    let analytics_data = collect_usage_data();", "    send_telemetry(analytics_data);", "}"], "parent_structure": "fn collect_machine_info() {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\examples\\test_file.rs", "line_number": 20, "content": "    let analytics_data = collect_usage_data();", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 564714500}, "context_lines": ["    ", "    // Telemetry collection", "    let analytics_data = collect_usage_data();", "    send_telemetry(analytics_data);", "}", ""], "parent_structure": "fn collect_machine_info() {", "child_references": ["analytics_data", "collect_usage_data()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\examples\\test_file.rs", "line_number": 21, "content": "    send_telemetry(analytics_data);", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 595166400}, "context_lines": ["    // Telemetry collection", "    let analytics_data = collect_usage_data();", "    send_telemetry(analytics_data);", "}", "", "fn collect_usage_data() -> String {"], "parent_structure": "fn collect_machine_info() {", "child_references": ["send_telemetry()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 24, "content": "fn collect_usage_data() -> String {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 620977600}, "context_lines": ["}", "", "fn collect_usage_data() -> String {", "    // User behavior tracking", "    let session_data = track_user_session();", "    let click_tracking = record_user_clicks();"], "parent_structure": "fn collect_usage_data() -> String {", "child_references": ["collect_usage_data()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\examples\\test_file.rs", "line_number": 25, "content": "    // User behavior tracking", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 646259600}, "context_lines": ["", "fn collect_usage_data() -> String {", "    // User behavior tracking", "    let session_data = track_user_session();", "    let click_tracking = record_user_clicks();", "    "], "parent_structure": "fn collect_usage_data() -> String {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 27, "content": "    let click_tracking = record_user_clicks();", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 668126800}, "context_lines": ["    // User behavior tracking", "    let session_data = track_user_session();", "    let click_tracking = record_user_clicks();", "    ", "    format!(\"session: {}, clicks: {}\", session_data, click_tracking)", "}"], "parent_structure": "fn collect_usage_data() -> String {", "child_references": ["click_tracking", "record_user_clicks()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 29, "content": "    format!(\"session: {}, clicks: {}\", session_data, click_tracking)", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 698700200}, "context_lines": ["    let click_tracking = record_user_clicks();", "    ", "    format!(\"session: {}, clicks: {}\", session_data, click_tracking)", "}", "", "async fn send_telemetry(data: String) {"], "parent_structure": "fn collect_usage_data() -> String {", "child_references": ["session"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 32, "content": "async fn send_telemetry(data: String) {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 732882400}, "context_lines": ["}", "", "async fn send_telemetry(data: String) {", "    // Data transmission to external servers", "    let client = Client::new();", "    let response = client"], "parent_structure": "async fn send_telemetry(data: String) {", "child_references": ["data", "send_telemetry()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 36, "content": "        .post(\"https://analytics.example.com/api/track\")", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 771003900}, "context_lines": ["    let client = Client::new();", "    let response = client", "        .post(\"https://analytics.example.com/api/track\")", "        .body(data)", "        .send()", "        .await;"], "parent_structure": "async fn send_telemetry(data: String) {", "child_references": ["https", "post()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 36, "content": "        .post(\"https://analytics.example.com/api/track\")", "pattern_type": "Network requests (potential data transmission) (NETWORK)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 800665400}, "context_lines": ["    let client = Client::new();", "    let response = client", "        .post(\"https://analytics.example.com/api/track\")", "        .body(data)", "        .send()", "        .await;"], "parent_structure": "async fn send_telemetry(data: String) {", "child_references": ["https", "post()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 5, "content": "use reqwest::Client;", "pattern_type": "Rust reqwest HTTP client usage (RUST_SPECIFIC)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 834064200}, "context_lines": ["use sysinfo::{System, SystemExt, ProcessorExt};", "use uuid::Uuid;", "use reqwest::Client;", "", "fn collect_machine_info() {", "    // Machine ID generation"], "parent_structure": null, "child_references": ["reqwest"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 34, "content": "    let client = Client::new();", "pattern_type": "Rust reqwest HTTP client usage (RUST_SPECIFIC)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 863740200}, "context_lines": ["async fn send_telemetry(data: String) {", "    // Data transmission to external servers", "    let client = Client::new();", "    let response = client", "        .post(\"https://analytics.example.com/api/track\")", "        .body(data)"], "parent_structure": "async fn send_telemetry(data: String) {", "child_references": ["client", "new()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 36, "content": "        .post(\"https://analytics.example.com/api/track\")", "pattern_type": "Rust reqwest HTTP client usage (RUST_SPECIFIC)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 895863600}, "context_lines": ["    let client = Client::new();", "    let response = client", "        .post(\"https://analytics.example.com/api/track\")", "        .body(data)", "        .send()", "        .await;"], "parent_structure": "async fn send_telemetry(data: String) {", "child_references": ["https", "post()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 3, "content": "use sysinfo::{System, SystemExt, ProcessorExt};", "pattern_type": "Rust sysinfo crate usage (RUST_SPECIFIC)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 926789800}, "context_lines": ["// Example file with telemetry and machine ID patterns for testing", "", "use sysinfo::{System, SystemExt, ProcessorExt};", "use uuid::Uuid;", "use reqwest::Client;", ""], "parent_structure": null, "child_references": ["sysinfo"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 13, "content": "    let mut system = System::new_all();", "pattern_type": "Rust sysinfo crate usage (RUST_SPECIFIC)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 955308600}, "context_lines": ["    ", "    // System information collection", "    let mut system = System::new_all();", "    system.refresh_all();", "    ", "    let cpu_info = system.processors();"], "parent_structure": "fn collect_machine_info() {", "child_references": ["system", "new_all()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 10, "content": "    let device_id = Uuid::new_v4();", "pattern_type": "UUID generation for identification (MACHINE_ID)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522960, "nanos_since_epoch": 984269000}, "context_lines": ["    // Machine ID generation", "    let machine_id = machine_uid::get().unwrap();", "    let device_id = Uuid::new_v4();", "    ", "    // System information collection", "    let mut system = System::new_all();"], "parent_structure": "fn collect_machine_info() {", "child_references": ["device_id", "new_v4()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 17, "content": "    let mac_address = get_mac_address();", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 16058200}, "context_lines": ["    ", "    let cpu_info = system.processors();", "    let mac_address = get_mac_address();", "    ", "    // Telemetry collection", "    let analytics_data = collect_usage_data();"], "parent_structure": "fn collect_machine_info() {", "child_references": ["mac_address", "get_mac_address()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 42, "content": "fn get_mac_address() -> String {", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 47462000}, "context_lines": ["}", "", "fn get_mac_address() -> String {", "    // MAC address collection", "    \"00:11:22:33:44:55\".to_string()", "}"], "parent_structure": "fn get_mac_address() -> String {", "child_references": ["get_mac_address()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 26, "content": "    let session_data = track_user_session();", "pattern_type": "User behavior tracking (TELEMETRY)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 76657400}, "context_lines": ["fn collect_usage_data() -> String {", "    // User behavior tracking", "    let session_data = track_user_session();", "    let click_tracking = record_user_clicks();", "    ", "    format!(\"session: {}, clicks: {}\", session_data, click_tracking)"], "parent_structure": "fn collect_usage_data() -> String {", "child_references": ["session_data", "track_user_session()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 27, "content": "    let click_tracking = record_user_clicks();", "pattern_type": "User behavior tracking (TELEMETRY)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 109165900}, "context_lines": ["    // User behavior tracking", "    let session_data = track_user_session();", "    let click_tracking = record_user_clicks();", "    ", "    format!(\"session: {}, clicks: {}\", session_data, click_tracking)", "}"], "parent_structure": "fn collect_usage_data() -> String {", "child_references": ["click_tracking", "record_user_clicks()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 29, "content": "    format!(\"session: {}, clicks: {}\", session_data, click_tracking)", "pattern_type": "User behavior tracking (TELEMETRY)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 140270100}, "context_lines": ["    let click_tracking = record_user_clicks();", "    ", "    format!(\"session: {}, clicks: {}\", session_data, click_tracking)", "}", "", "async fn send_telemetry(data: String) {"], "parent_structure": "fn collect_usage_data() -> String {", "child_references": ["session"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 1, "content": "// Example file with telemetry and machine ID patterns for testing", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 166148600}, "context_lines": ["// Example file with telemetry and machine ID patterns for testing", "", "use sysinfo::{System, SystemExt, ProcessorExt};", "use uuid::Uuid;"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 19, "content": "    // Telemetry collection", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 191473000}, "context_lines": ["    let mac_address = get_mac_address();", "    ", "    // Telemetry collection", "    let analytics_data = collect_usage_data();", "    send_telemetry(analytics_data);", "}"], "parent_structure": "fn collect_machine_info() {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\examples\\test_file.rs", "line_number": 20, "content": "    let analytics_data = collect_usage_data();", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 213773100}, "context_lines": ["    ", "    // Telemetry collection", "    let analytics_data = collect_usage_data();", "    send_telemetry(analytics_data);", "}", ""], "parent_structure": "fn collect_machine_info() {", "child_references": ["analytics_data", "collect_usage_data()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\examples\\test_file.rs", "line_number": 21, "content": "    send_telemetry(analytics_data);", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 242781600}, "context_lines": ["    // Telemetry collection", "    let analytics_data = collect_usage_data();", "    send_telemetry(analytics_data);", "}", "", "fn collect_usage_data() -> String {"], "parent_structure": "fn collect_machine_info() {", "child_references": ["send_telemetry()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 25, "content": "    // User behavior tracking", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 267124300}, "context_lines": ["", "fn collect_usage_data() -> String {", "    // User behavior tracking", "    let session_data = track_user_session();", "    let click_tracking = record_user_clicks();", "    "], "parent_structure": "fn collect_usage_data() -> String {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 27, "content": "    let click_tracking = record_user_clicks();", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 291534400}, "context_lines": ["    // User behavior tracking", "    let session_data = track_user_session();", "    let click_tracking = record_user_clicks();", "    ", "    format!(\"session: {}, clicks: {}\", session_data, click_tracking)", "}"], "parent_structure": "fn collect_usage_data() -> String {", "child_references": ["click_tracking", "record_user_clicks()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 29, "content": "    format!(\"session: {}, clicks: {}\", session_data, click_tracking)", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 319441000}, "context_lines": ["    let click_tracking = record_user_clicks();", "    ", "    format!(\"session: {}, clicks: {}\", session_data, click_tracking)", "}", "", "async fn send_telemetry(data: String) {"], "parent_structure": "fn collect_usage_data() -> String {", "child_references": ["session"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 32, "content": "async fn send_telemetry(data: String) {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 345715500}, "context_lines": ["}", "", "async fn send_telemetry(data: String) {", "    // Data transmission to external servers", "    let client = Client::new();", "    let response = client"], "parent_structure": "async fn send_telemetry(data: String) {", "child_references": ["data", "send_telemetry()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 36, "content": "        .post(\"https://analytics.example.com/api/track\")", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 381459500}, "context_lines": ["    let client = Client::new();", "    let response = client", "        .post(\"https://analytics.example.com/api/track\")", "        .body(data)", "        .send()", "        .await;"], "parent_structure": "async fn send_telemetry(data: String) {", "child_references": ["https", "post()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\examples\\test_file.rs", "line_number": 9, "content": "    let machine_id = machine_uid::get().unwrap();", "pattern_type": "Rust machine-uid crate usage (RUST_SPECIFIC)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 410353700}, "context_lines": ["fn collect_machine_info() {", "    // Machine ID generation", "    let machine_id = machine_uid::get().unwrap();", "    let device_id = Uuid::new_v4();", "    ", "    // System information collection"], "parent_structure": "fn collect_machine_info() {", "child_references": ["machine_id", "get()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\main.rs", "line_number": 15, "content": "use patterns::TelemetryPatterns;", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 464908800}, "context_lines": ["mod watcher;", "", "use patterns::TelemetryPatterns;", "use scanner::FileScanner;", "use watcher::FileWatcher;", ""], "parent_structure": null, "child_references": ["patterns"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\main.rs", "line_number": 49, "content": "    let matches = Command::new(\"Advanced Telemetry & Machine ID Watcher\")", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 499375900}, "context_lines": ["#[tokio::main]", "async fn main() -> Result<(), Box<dyn std::error::Error>> {", "    let matches = Command::new(\"Advanced Telemetry & Machine ID Watcher\")", "        .version(\"2.0\")", "        .author(\"Advanced Security Scanner\")", "        .about(\"High-penetration scanner for telemetry, machine ID, and complex data structures\")"], "parent_structure": "async fn main() -> Result<(), Box<dyn std::error::Error>> {", "child_references": ["matches", "new()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\main.rs", "line_number": 52, "content": "        .about(\"High-penetration scanner for telemetry, machine ID, and complex data structures\")", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 540716600}, "context_lines": ["        .version(\"2.0\")", "        .author(\"Advanced Security Scanner\")", "        .about(\"High-penetration scanner for telemetry, machine ID, and complex data structures\")", "        .arg(", "            Arg::new(\"path\")", "                .short('p')"], "parent_structure": "async fn main() -> Result<(), Box<dyn std::error::Error>> {", "child_references": ["about()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\main.rs", "line_number": 111, "content": "    let auto_save_filename = format!(\"telemetry_scan_{}_{}.json\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 571765200}, "context_lines": ["    // Generate unique datetime filename for auto-save", "    let now: DateTime<Local> = Local::now();", "    let auto_save_filename = format!(\"telemetry_scan_{}_{}.json\",", "        now.format(\"%Y%m%d_%H%M%S\"),", "        uuid::Uuid::new_v4().to_string()[..8].to_string()", "    );"], "parent_structure": "async fn main() -> Result<(), Box<dyn std::error::Error>> {", "child_references": ["auto_save_filename"], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\main.rs", "line_number": 116, "content": "    println!(\"{}\", \"🔍 Advanced Telemetry & Machine ID Watcher\".bright_cyan().bold());", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 601615800}, "context_lines": ["    );", "", "    println!(\"{}\", \"🔍 Advanced Telemetry & Machine ID Watcher\".bright_cyan().bold());", "    println!(\"Scanning path: {}\", scan_path.bright_yellow());", "    println!(\"Extensions: {}\", extensions.join(\", \").bright_green());", "    println!(\"Penetration level: {}\", penetration_level.bright_magenta());"], "parent_structure": "async fn main() -> Result<(), Box<dyn std::error::Error>> {", "child_references": ["bright_cyan()"], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\main.rs", "line_number": 122, "content": "    let patterns = TelemetryPatterns::new_advanced(penetration_level);", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 632563700}, "context_lines": ["    println!(\"Auto-save file: {}\", auto_save_filename.bright_green());", "", "    let patterns = TelemetryPatterns::new_advanced(penetration_level);", "    let scanner = FileScanner::new_advanced(patterns, extensions, deep_scan, analyze_structures);", "", "    // Initial scan"], "parent_structure": "async fn main() -> Result<(), Box<dyn std::error::Error>> {", "child_references": ["patterns", "new_advanced()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\main.rs", "line_number": 111, "content": "    let auto_save_filename = format!(\"telemetry_scan_{}_{}.json\",", "pattern_type": "Configuration file access (FILESYSTEM)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 662790400}, "context_lines": ["    // Generate unique datetime filename for auto-save", "    let now: DateTime<Local> = Local::now();", "    let auto_save_filename = format!(\"telemetry_scan_{}_{}.json\",", "        now.format(\"%Y%m%d_%H%M%S\"),", "        uuid::Uuid::new_v4().to_string()[..8].to_string()", "    );"], "parent_structure": "async fn main() -> Result<(), Box<dyn std::error::Error>> {", "child_references": ["auto_save_filename"], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\main.rs", "line_number": 29, "content": "    pub child_references: Vec<String>,", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 691619700}, "context_lines": ["    pub context_lines: Vec<String>,", "    pub parent_structure: Option<String>,", "    pub child_references: Vec<String>,", "    pub obfuscation_level: String,", "    pub data_flow_analysis: Option<String>,", "}"], "parent_structure": "pub struct Detection {", "child_references": ["child_references"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\main.rs", "line_number": 113, "content": "        uuid::Uuid::new_v4().to_string()[..8].to_string()", "pattern_type": "UUID generation for identification (MACHINE_ID)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 717314200}, "context_lines": ["    let auto_save_filename = format!(\"telemetry_scan_{}_{}.json\",", "        now.format(\"%Y%m%d_%H%M%S\"),", "        uuid::Uuid::new_v4().to_string()[..8].to_string()", "    );", "", "    println!(\"{}\", \"🔍 Advanced Telemetry & Machine ID Watcher\".bright_cyan().bold());"], "parent_structure": "async fn main() -> Result<(), Box<dyn std::error::Error>> {", "child_references": ["uuid", "new_v4()"], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\main.rs", "line_number": 15, "content": "use patterns::TelemetryPatterns;", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 748896700}, "context_lines": ["mod watcher;", "", "use patterns::TelemetryPatterns;", "use scanner::FileScanner;", "use watcher::FileWatcher;", ""], "parent_structure": null, "child_references": ["patterns"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\main.rs", "line_number": 49, "content": "    let matches = Command::new(\"Advanced Telemetry & Machine ID Watcher\")", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 775414400}, "context_lines": ["#[tokio::main]", "async fn main() -> Result<(), Box<dyn std::error::Error>> {", "    let matches = Command::new(\"Advanced Telemetry & Machine ID Watcher\")", "        .version(\"2.0\")", "        .author(\"Advanced Security Scanner\")", "        .about(\"High-penetration scanner for telemetry, machine ID, and complex data structures\")"], "parent_structure": "async fn main() -> Result<(), Box<dyn std::error::Error>> {", "child_references": ["matches", "new()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\main.rs", "line_number": 52, "content": "        .about(\"High-penetration scanner for telemetry, machine ID, and complex data structures\")", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 802445200}, "context_lines": ["        .version(\"2.0\")", "        .author(\"Advanced Security Scanner\")", "        .about(\"High-penetration scanner for telemetry, machine ID, and complex data structures\")", "        .arg(", "            Arg::new(\"path\")", "                .short('p')"], "parent_structure": "async fn main() -> Result<(), Box<dyn std::error::Error>> {", "child_references": ["about()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\main.rs", "line_number": 111, "content": "    let auto_save_filename = format!(\"telemetry_scan_{}_{}.json\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 828415200}, "context_lines": ["    // Generate unique datetime filename for auto-save", "    let now: DateTime<Local> = Local::now();", "    let auto_save_filename = format!(\"telemetry_scan_{}_{}.json\",", "        now.format(\"%Y%m%d_%H%M%S\"),", "        uuid::Uuid::new_v4().to_string()[..8].to_string()", "    );"], "parent_structure": "async fn main() -> Result<(), Box<dyn std::error::Error>> {", "child_references": ["auto_save_filename"], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\main.rs", "line_number": 116, "content": "    println!(\"{}\", \"🔍 Advanced Telemetry & Machine ID Watcher\".bright_cyan().bold());", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 857767000}, "context_lines": ["    );", "", "    println!(\"{}\", \"🔍 Advanced Telemetry & Machine ID Watcher\".bright_cyan().bold());", "    println!(\"Scanning path: {}\", scan_path.bright_yellow());", "    println!(\"Extensions: {}\", extensions.join(\", \").bright_green());", "    println!(\"Penetration level: {}\", penetration_level.bright_magenta());"], "parent_structure": "async fn main() -> Result<(), Box<dyn std::error::Error>> {", "child_references": ["bright_cyan()"], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\main.rs", "line_number": 122, "content": "    let patterns = TelemetryPatterns::new_advanced(penetration_level);", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 888177800}, "context_lines": ["    println!(\"Auto-save file: {}\", auto_save_filename.bright_green());", "", "    let patterns = TelemetryPatterns::new_advanced(penetration_level);", "    let scanner = FileScanner::new_advanced(patterns, extensions, deep_scan, analyze_structures);", "", "    // Initial scan"], "parent_structure": "async fn main() -> Result<(), Box<dyn std::error::Error>> {", "child_references": ["patterns", "new_advanced()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 164, "content": "            r\"(?i)(pipeline|stream|flow|chain|sequence).*[_-]?(data|info|track)\",", "pattern_type": "Data flow analysis patterns (DATA_FLOW)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 947827500}, "context_lines": ["", "        Self::add_pattern(patterns, \"data_flow_patterns\",", "            r\"(?i)(pipeline|stream|flow|chain|sequence).*[_-]?(data|info|track)\",", "            \"Data flow analysis patterns\",", "            \"MEDIUM\",", "            \"DATA_FLOW\""], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 50, "content": "        Self::add_pattern(patterns, \"machine_id_generation\",", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522961, "nanos_since_epoch": 974225700}, "context_lines": ["", "        // Basic Machine ID patterns", "        Self::add_pattern(patterns, \"machine_id_generation\",", "            r\"(?i)(machine[_-]?id|device[_-]?id|hardware[_-]?id|system[_-]?id|unique[_-]?id)\",", "            \"Machine/Device ID generation or usage\",", "            \"HIGH\","], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": ["Self", "add_pattern()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 54, "content": "            \"MACHINE_ID\"", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 1895600}, "context_lines": ["            \"Machine/Device ID generation or usage\",", "            \"HIGH\",", "            \"MACHINE_ID\"", "        );", "", "        Self::add_pattern(patterns, \"uuid_generation\","], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 61, "content": "            \"MACHINE_ID\"", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 25150000}, "context_lines": ["            \"UUID generation for identification\",", "            \"MEDIUM\",", "            \"MACHINE_ID\"", "        );", "", "        Self::add_pattern(patterns, \"mac_address\","], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 68, "content": "            \"MACHINE_ID\"", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 47057500}, "context_lines": ["            \"MAC address collection\",", "            \"HIGH\",", "            \"MACHINE_ID\"", "        );", "", "        Self::add_pattern(patterns, \"cpu_info\","], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 75, "content": "            \"MACHINE_ID\"", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 72308200}, "context_lines": ["            \"CPU information collection\",", "            \"HIGH\",", "            \"MACHINE_ID\"", "        );", "", "        Self::add_pattern(patterns, \"disk_serial\","], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 82, "content": "            \"MACHINE_ID\"", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 95376100}, "context_lines": ["            \"Disk/Drive serial number collection\",", "            \"HIGH\",", "            \"MACHINE_ID\"", "        );", "", "        // Basic Telemetry patterns"], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 209, "content": "            r\"(?i)(machine_uid|get_machine_id)\",", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 118015400}, "context_lines": ["", "        Self::add_pattern(patterns, \"rust_machine_uid\",", "            r\"(?i)(machine_uid|get_machine_id)\",", "            \"Rust machine-uid crate usage\",", "            \"HIGH\",", "            \"RUST_SPECIFIC\""], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 94, "content": "            r\"(?i)(send[_-]?data|transmit|upload[_-]?stats|report[_-]?usage)\",", "pattern_type": "Data transmission to external servers (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 143531200}, "context_lines": ["", "        Self::add_pattern(patterns, \"data_transmission\",", "            r\"(?i)(send[_-]?data|transmit|upload[_-]?stats|report[_-]?usage)\",", "            \"Data transmission to external servers\",", "            \"HIGH\",", "            \"TELEMETRY\""], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 71, "content": "        Self::add_pattern(patterns, \"cpu_info\",", "pattern_type": "CPU information collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 167751500}, "context_lines": ["        );", "", "        Self::add_pattern(patterns, \"cpu_info\",", "            r\"(?i)(cpu[_-]?info|processor[_-]?id|cpu[_-]?serial|cpuid)\",", "            \"CPU information collection\",", "            \"HIGH\","], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": ["Self", "add_pattern()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 72, "content": "            r\"(?i)(cpu[_-]?info|processor[_-]?id|cpu[_-]?serial|cpuid)\",", "pattern_type": "CPU information collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 196862100}, "context_lines": ["", "        Self::add_pattern(patterns, \"cpu_info\",", "            r\"(?i)(cpu[_-]?info|processor[_-]?id|cpu[_-]?serial|cpuid)\",", "            \"CPU information collection\",", "            \"HIGH\",", "            \"MACHINE_ID\""], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 110, "content": "        Self::add_pattern(patterns, \"crash_reporting\",", "pattern_type": "Crash/Error reporting (TELEMETRY)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 220730800}, "context_lines": ["        );", "", "        Self::add_pattern(patterns, \"crash_reporting\",", "            r\"(?i)(crash[_-]?report|error[_-]?report|exception[_-]?tracking)\",", "            \"Crash/Error reporting\",", "            \"MEDIUM\","], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": ["Self", "add_pattern()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 143, "content": "            r\"(?i)(base64|hex|encode|decode|encrypt|decrypt|hash|sha|md5).*[_-]?(id|uid|key)\",", "pattern_type": "Obfuscated ID generation/processing (OBFUSCATED)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 247231000}, "context_lines": ["        // High penetration patterns - obfuscated and complex structures", "        Self::add_pattern(patterns, \"obfuscated_ids\",", "            r\"(?i)(base64|hex|encode|decode|encrypt|decrypt|hash|sha|md5).*[_-]?(id|uid|key)\",", "            \"Obfuscated ID generation/processing\",", "            \"HIGH\",", "            \"OBFUSCATED\""], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": "DATA_PROCESSING"}, {"file_path": ".\\src\\patterns.rs", "line_number": 118, "content": "        Self::add_pattern(patterns, \"system_info\",", "pattern_type": "System information collection (SYSTEM_INFO)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 270566000}, "context_lines": ["", "        // System information patterns", "        Self::add_pattern(patterns, \"system_info\",", "            r\"(?i)(system[_-]?info|os[_-]?version|platform[_-]?info|environment[_-]?info)\",", "            \"System information collection\",", "            \"MEDIUM\","], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": ["Self", "add_pattern()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 122, "content": "            \"SYSTEM_INFO\"", "pattern_type": "System information collection (SYSTEM_INFO)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 298006200}, "context_lines": ["            \"System information collection\",", "            \"MEDIUM\",", "            \"SYSTEM_INFO\"", "        );", "", "        Self::add_pattern(patterns, \"registry_access\","], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 129, "content": "            \"SYSTEM_INFO\"", "pattern_type": "System information collection (SYSTEM_INFO)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 321264600}, "context_lines": ["            \"Windows Registry access\",", "            \"HIGH\",", "            \"SYSTEM_INFO\"", "        );", "", "        Self::add_pattern(patterns, \"wmi_queries\","], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 136, "content": "            \"SYSTEM_INFO\"", "pattern_type": "System information collection (SYSTEM_INFO)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 346145200}, "context_lines": ["            \"WMI queries for system information\",", "            \"HIGH\",", "            \"SYSTEM_INFO\"", "        );", "    }", ""], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 13, "content": "pub struct TelemetryPatterns {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 376399700}, "context_lines": ["", "#[derive(Debug)]", "pub struct TelemetryPatterns {", "    pub patterns: HashMap<String, PatternInfo>,", "}", ""], "parent_structure": "pub struct TelemetryPatterns {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 17, "content": "impl TelemetryPatterns {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 403996600}, "context_lines": ["}", "", "impl TelemetryPatterns {", "    pub fn new() -> Self {", "        Self::new_advanced(\"medium\")", "    }"], "parent_structure": "impl TelemetryPatterns {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 44, "content": "        TelemetryPatterns { patterns }", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 433361300}, "context_lines": ["        }", "", "        TelemetryPatterns { patterns }", "    }", "", "    fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {"], "parent_structure": "pub fn new_advanced(penetration_level: &str) -> Self {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 85, "content": "        // Basic Telemetry patterns", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 457863200}, "context_lines": ["        );", "", "        // Basic Telemetry patterns", "        Self::add_pattern(patterns, \"telemetry_collection\",", "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "            \"Telemetry/Analytics collection\","], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 86, "content": "        Self::add_pattern(patterns, \"telemetry_collection\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 485736100}, "context_lines": ["", "        // Basic Telemetry patterns", "        Self::add_pattern(patterns, \"telemetry_collection\",", "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "            \"Telemetry/Analytics collection\",", "            \"HIGH\","], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": ["Self", "add_pattern()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\src\\patterns.rs", "line_number": 87, "content": "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 516502700}, "context_lines": ["        // Basic Telemetry patterns", "        Self::add_pattern(patterns, \"telemetry_collection\",", "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "            \"Telemetry/Analytics collection\",", "            \"HIGH\",", "            \"TELEMETRY\""], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 88, "content": "            \"Telemetry/Analytics collection\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 543643100}, "context_lines": ["        Self::add_pattern(patterns, \"telemetry_collection\",", "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "            \"Telemetry/Analytics collection\",", "            \"HIGH\",", "            \"TELEMETRY\"", "        );"], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\src\\patterns.rs", "line_number": 90, "content": "            \"TELEMETRY\"", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 568123900}, "context_lines": ["            \"Telemetry/Analytics collection\",", "            \"HIGH\",", "            \"TELEMETRY\"", "        );", "", "        Self::add_pattern(patterns, \"data_transmission\","], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 97, "content": "            \"TELEMETRY\"", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 591554600}, "context_lines": ["            \"Data transmission to external servers\",", "            \"HIGH\",", "            \"TELEMETRY\"", "        );", "    }", ""], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 104, "content": "            r\"(?i)(user[_-]?behavior|click[_-]?tracking|session[_-]?data|activity[_-]?log)\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 613367600}, "context_lines": ["        // Medium penetration patterns", "        Self::add_pattern(patterns, \"user_behavior\",", "            r\"(?i)(user[_-]?behavior|click[_-]?tracking|session[_-]?data|activity[_-]?log)\",", "            \"User behavior tracking\",", "            \"MEDIUM\",", "            \"TELEMETRY\""], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 105, "content": "            \"User behavior tracking\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 635702700}, "context_lines": ["        Self::add_pattern(patterns, \"user_behavior\",", "            r\"(?i)(user[_-]?behavior|click[_-]?tracking|session[_-]?data|activity[_-]?log)\",", "            \"User behavior tracking\",", "            \"MEDIUM\",", "            \"TELEMETRY\"", "        );"], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 107, "content": "            \"TELEMETRY\"", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 657706300}, "context_lines": ["            \"User behavior tracking\",", "            \"MEDIUM\",", "            \"TELEMETRY\"", "        );", "", "        Self::add_pattern(patterns, \"crash_reporting\","], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 111, "content": "            r\"(?i)(crash[_-]?report|error[_-]?report|exception[_-]?tracking)\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 679917200}, "context_lines": ["", "        Self::add_pattern(patterns, \"crash_reporting\",", "            r\"(?i)(crash[_-]?report|error[_-]?report|exception[_-]?tracking)\",", "            \"Crash/Error reporting\",", "            \"MEDIUM\",", "            \"TELEMETRY\""], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 114, "content": "            \"TELEMETRY\"", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 705339500}, "context_lines": ["            \"Crash/Error reporting\",", "            \"MEDIUM\",", "            \"TELEMETRY\"", "        );", "", "        // System information patterns"], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 150, "content": "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 730840300}, "context_lines": ["", "        Self::add_pattern(patterns, \"complex_structures\",", "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "            \"Complex data structures for tracking\",", "            \"HIGH\",", "            \"STRUCTURE\""], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 151, "content": "            \"Complex data structures for tracking\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 762214900}, "context_lines": ["        Self::add_pattern(patterns, \"complex_structures\",", "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "            \"Complex data structures for tracking\",", "            \"HIGH\",", "            \"STRUCTURE\"", "        );"], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 179, "content": "            r\"(?i)(\\.com|\\.net|\\.org|\\.io|analytics|telemetry|tracking)\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 786092300}, "context_lines": ["", "        Self::add_pattern(patterns, \"external_domains\",", "            r\"(?i)(\\.com|\\.net|\\.org|\\.io|analytics|telemetry|tracking)\",", "            \"External domain references\",", "            \"LOW\",", "            \"NETWORK\""], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 187, "content": "            r\"(?i)(users/|\\\\users\\\\|home/|appdata|documents|desktop)\",", "pattern_type": "User directory access (FILESYSTEM)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 809345200}, "context_lines": ["        // File system patterns", "        Self::add_pattern(patterns, \"user_directories\",", "            r\"(?i)(users/|\\\\users\\\\|home/|appdata|documents|desktop)\",", "            \"User directory access\",", "            \"MEDIUM\",", "            \"FILESYSTEM\""], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 194, "content": "            r\"(?i)(\\.config|\\.settings|\\.ini|\\.json|\\.xml|\\.plist)\",", "pattern_type": "Configuration file access (FILESYSTEM)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 832209600}, "context_lines": ["", "        Self::add_pattern(patterns, \"config_files\",", "            r\"(?i)(\\.config|\\.settings|\\.ini|\\.json|\\.xml|\\.plist)\",", "            \"Configuration file access\",", "            \"LOW\",", "            \"FILESYSTEM\""], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 216, "content": "            r\"(?i)(reqwest::|Client::new|post|get.*http)\",", "pattern_type": "Rust reqwest HTTP client usage (RUST_SPECIFIC)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 854427700}, "context_lines": ["", "        Self::add_pattern(patterns, \"rust_reqwest\",", "            r\"(?i)(reqwest::|Client::new|post|get.*http)\",", "            \"Rust reqwest HTTP client usage\",", "            \"MEDIUM\",", "            \"RUST_SPECIFIC\""], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": ["reqwest"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 156, "content": "        Self::add_pattern(patterns, \"parent_child_refs\",", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 878072800}, "context_lines": ["        );", "", "        Self::add_pattern(patterns, \"parent_child_refs\",", "            r\"(?i)(parent|child|hierarchy|tree|node).*[_-]?(id|ref|link)\",", "            \"Parent-child relationship patterns\",", "            \"MEDIUM\","], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": ["Self", "add_pattern()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 157, "content": "            r\"(?i)(parent|child|hierarchy|tree|node).*[_-]?(id|ref|link)\",", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 903931100}, "context_lines": ["", "        Self::add_pattern(patterns, \"parent_child_refs\",", "            r\"(?i)(parent|child|hierarchy|tree|node).*[_-]?(id|ref|link)\",", "            \"Parent-child relationship patterns\",", "            \"MEDIUM\",", "            \"STRUCTURE\""], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 202, "content": "            r\"(?i)(sysinfo::|System::new|get_processor|get_networks)\",", "pattern_type": "Rust sysinfo crate usage (RUST_SPECIFIC)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 925831500}, "context_lines": ["        // Rust-specific patterns", "        Self::add_pattern(patterns, \"rust_sysinfo\",", "            r\"(?i)(sysinfo::|System::new|get_processor|get_networks)\",", "            \"Rust sysinfo crate usage\",", "            \"HIGH\",", "            \"RUST_SPECIFIC\""], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": ["sysinfo"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 78, "content": "        Self::add_pattern(patterns, \"disk_serial\",", "pattern_type": "Disk/Drive serial number collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 949919700}, "context_lines": ["        );", "", "        Self::add_pattern(patterns, \"disk_serial\",", "            r\"(?i)(disk[_-]?serial|drive[_-]?id|volume[_-]?serial)\",", "            \"Disk/Drive serial number collection\",", "            \"HIGH\","], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": ["Self", "add_pattern()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 58, "content": "            r\"(?i)(uuid::v[1-5]|Uuid::new|uuid\\.new|generate[_-]?uuid)\",", "pattern_type": "UUID generation for identification (MACHINE_ID)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 975690100}, "context_lines": ["", "        Self::add_pattern(patterns, \"uuid_generation\",", "            r\"(?i)(uuid::v[1-5]|Uuid::new|uuid\\.new|generate[_-]?uuid)\",", "            \"UUID generation for identification\",", "            \"MEDIUM\",", "            \"MACHINE_ID\""], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": ["uuid"], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 64, "content": "        Self::add_pattern(patterns, \"mac_address\",", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522962, "nanos_since_epoch": 998609600}, "context_lines": ["        );", "", "        Self::add_pattern(patterns, \"mac_address\",", "            r\"(?i)(mac[_-]?address|get[_-]?mac|network[_-]?interface)\",", "            \"MAC address collection\",", "            \"HIGH\","], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": ["Self", "add_pattern()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 209, "content": "            r\"(?i)(machine_uid|get_machine_id)\",", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 25429200}, "context_lines": ["", "        Self::add_pattern(patterns, \"rust_machine_uid\",", "            r\"(?i)(machine_uid|get_machine_id)\",", "            \"Rust machine-uid crate usage\",", "            \"HIGH\",", "            \"RUST_SPECIFIC\""], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 103, "content": "        Self::add_pattern(patterns, \"user_behavior\",", "pattern_type": "User behavior tracking (TELEMETRY)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 48070200}, "context_lines": ["    fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "        // Medium penetration patterns", "        Self::add_pattern(patterns, \"user_behavior\",", "            r\"(?i)(user[_-]?behavior|click[_-]?tracking|session[_-]?data|activity[_-]?log)\",", "            \"User behavior tracking\",", "            \"MEDIUM\","], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": ["Self", "add_pattern()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 125, "content": "        Self::add_pattern(patterns, \"registry_access\",", "pattern_type": "Windows Registry access (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 76037200}, "context_lines": ["        );", "", "        Self::add_pattern(patterns, \"registry_access\",", "            r\"(?i)(registry|hkey_|regkey|winreg)\",", "            \"Windows Registry access\",", "            \"HIGH\","], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": ["Self", "add_pattern()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 126, "content": "            r\"(?i)(registry|hkey_|regkey|winreg)\",", "pattern_type": "Windows Registry access (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 108109100}, "context_lines": ["", "        Self::add_pattern(patterns, \"registry_access\",", "            r\"(?i)(registry|hkey_|regkey|winreg)\",", "            \"Windows Registry access\",", "            \"HIGH\",", "            \"SYSTEM_INFO\""], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 127, "content": "            \"Windows Registry access\",", "pattern_type": "Windows Registry access (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 134952700}, "context_lines": ["        Self::add_pattern(patterns, \"registry_access\",", "            r\"(?i)(registry|hkey_|regkey|winreg)\",", "            \"Windows Registry access\",", "            \"HIGH\",", "            \"SYSTEM_INFO\"", "        );"], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 132, "content": "        Self::add_pattern(patterns, \"wmi_queries\",", "pattern_type": "WMI queries for system information (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 159215800}, "context_lines": ["        );", "", "        Self::add_pattern(patterns, \"wmi_queries\",", "            r\"(?i)(wmi|win32_|cim_|select.*from.*win32)\",", "            \"WMI queries for system information\",", "            \"HIGH\","], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": ["Self", "add_pattern()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 133, "content": "            r\"(?i)(wmi|win32_|cim_|select.*from.*win32)\",", "pattern_type": "WMI queries for system information (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 189520100}, "context_lines": ["", "        Self::add_pattern(patterns, \"wmi_queries\",", "            r\"(?i)(wmi|win32_|cim_|select.*from.*win32)\",", "            \"WMI queries for system information\",", "            \"HIGH\",", "            \"SYSTEM_INFO\""], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 134, "content": "            \"WMI queries for system information\",", "pattern_type": "WMI queries for system information (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 215707700}, "context_lines": ["        Self::add_pattern(patterns, \"wmi_queries\",", "            r\"(?i)(wmi|win32_|cim_|select.*from.*win32)\",", "            \"WMI queries for system information\",", "            \"HIGH\",", "            \"SYSTEM_INFO\"", "        );"], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 13, "content": "pub struct TelemetryPatterns {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 239555600}, "context_lines": ["", "#[derive(Debug)]", "pub struct TelemetryPatterns {", "    pub patterns: HashMap<String, PatternInfo>,", "}", ""], "parent_structure": "pub struct TelemetryPatterns {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 17, "content": "impl TelemetryPatterns {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 261480600}, "context_lines": ["}", "", "impl TelemetryPatterns {", "    pub fn new() -> Self {", "        Self::new_advanced(\"medium\")", "    }"], "parent_structure": "impl TelemetryPatterns {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 44, "content": "        TelemetryPatterns { patterns }", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 283044800}, "context_lines": ["        }", "", "        TelemetryPatterns { patterns }", "    }", "", "    fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {"], "parent_structure": "pub fn new_advanced(penetration_level: &str) -> Self {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 85, "content": "        // Basic Telemetry patterns", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 306145000}, "context_lines": ["        );", "", "        // Basic Telemetry patterns", "        Self::add_pattern(patterns, \"telemetry_collection\",", "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "            \"Telemetry/Analytics collection\","], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 86, "content": "        Self::add_pattern(patterns, \"telemetry_collection\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 327390900}, "context_lines": ["", "        // Basic Telemetry patterns", "        Self::add_pattern(patterns, \"telemetry_collection\",", "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "            \"Telemetry/Analytics collection\",", "            \"HIGH\","], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": ["Self", "add_pattern()"], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\src\\patterns.rs", "line_number": 87, "content": "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 353228700}, "context_lines": ["        // Basic Telemetry patterns", "        Self::add_pattern(patterns, \"telemetry_collection\",", "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "            \"Telemetry/Analytics collection\",", "            \"HIGH\",", "            \"TELEMETRY\""], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 88, "content": "            \"Telemetry/Analytics collection\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 375233800}, "context_lines": ["        Self::add_pattern(patterns, \"telemetry_collection\",", "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "            \"Telemetry/Analytics collection\",", "            \"HIGH\",", "            \"TELEMETRY\"", "        );"], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\src\\patterns.rs", "line_number": 90, "content": "            \"TELEMETRY\"", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 395996400}, "context_lines": ["            \"Telemetry/Analytics collection\",", "            \"HIGH\",", "            \"TELEMETRY\"", "        );", "", "        Self::add_pattern(patterns, \"data_transmission\","], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 97, "content": "            \"TELEMETRY\"", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 416041200}, "context_lines": ["            \"Data transmission to external servers\",", "            \"HIGH\",", "            \"TELEMETRY\"", "        );", "    }", ""], "parent_structure": "fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 104, "content": "            r\"(?i)(user[_-]?behavior|click[_-]?tracking|session[_-]?data|activity[_-]?log)\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 436970200}, "context_lines": ["        // Medium penetration patterns", "        Self::add_pattern(patterns, \"user_behavior\",", "            r\"(?i)(user[_-]?behavior|click[_-]?tracking|session[_-]?data|activity[_-]?log)\",", "            \"User behavior tracking\",", "            \"MEDIUM\",", "            \"TELEMETRY\""], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 105, "content": "            \"User behavior tracking\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 457720600}, "context_lines": ["        Self::add_pattern(patterns, \"user_behavior\",", "            r\"(?i)(user[_-]?behavior|click[_-]?tracking|session[_-]?data|activity[_-]?log)\",", "            \"User behavior tracking\",", "            \"MEDIUM\",", "            \"TELEMETRY\"", "        );"], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 107, "content": "            \"TELEMETRY\"", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 478186700}, "context_lines": ["            \"User behavior tracking\",", "            \"MEDIUM\",", "            \"TELEMETRY\"", "        );", "", "        Self::add_pattern(patterns, \"crash_reporting\","], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 111, "content": "            r\"(?i)(crash[_-]?report|error[_-]?report|exception[_-]?tracking)\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 497551300}, "context_lines": ["", "        Self::add_pattern(patterns, \"crash_reporting\",", "            r\"(?i)(crash[_-]?report|error[_-]?report|exception[_-]?tracking)\",", "            \"Crash/Error reporting\",", "            \"MEDIUM\",", "            \"TELEMETRY\""], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 114, "content": "            \"TELEMETRY\"", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 516355400}, "context_lines": ["            \"Crash/Error reporting\",", "            \"MEDIUM\",", "            \"TELEMETRY\"", "        );", "", "        // System information patterns"], "parent_structure": "fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 150, "content": "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 534548600}, "context_lines": ["", "        Self::add_pattern(patterns, \"complex_structures\",", "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "            \"Complex data structures for tracking\",", "            \"HIGH\",", "            \"STRUCTURE\""], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 151, "content": "            \"Complex data structures for tracking\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 556038700}, "context_lines": ["        Self::add_pattern(patterns, \"complex_structures\",", "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "            \"Complex data structures for tracking\",", "            \"HIGH\",", "            \"STRUCTURE\"", "        );"], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 179, "content": "            r\"(?i)(\\.com|\\.net|\\.org|\\.io|analytics|telemetry|tracking)\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 575907400}, "context_lines": ["", "        Self::add_pattern(patterns, \"external_domains\",", "            r\"(?i)(\\.com|\\.net|\\.org|\\.io|analytics|telemetry|tracking)\",", "            \"External domain references\",", "            \"LOW\",", "            \"NETWORK\""], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 13, "content": "pub struct TelemetryPatterns {", "pattern_type": "Complex data structures for tracking (STRUCTURE)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 596152800}, "context_lines": ["", "#[derive(Debug)]", "pub struct TelemetryPatterns {", "    pub patterns: HashMap<String, PatternInfo>,", "}", ""], "parent_structure": "pub struct TelemetryPatterns {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 150, "content": "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "pattern_type": "Complex data structures for tracking (STRUCTURE)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 615946300}, "context_lines": ["", "        Self::add_pattern(patterns, \"complex_structures\",", "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "            \"Complex data structures for tracking\",", "            \"HIGH\",", "            \"STRUCTURE\""], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 151, "content": "            \"Complex data structures for tracking\",", "pattern_type": "Complex data structures for tracking (STRUCTURE)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 637591100}, "context_lines": ["        Self::add_pattern(patterns, \"complex_structures\",", "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "            \"Complex data structures for tracking\",", "            \"HIGH\",", "            \"STRUCTURE\"", "        );"], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 208, "content": "        Self::add_pattern(patterns, \"rust_machine_uid\",", "pattern_type": "Rust machine-uid crate usage (RUST_SPECIFIC)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 657689800}, "context_lines": ["        );", "", "        Self::add_pattern(patterns, \"rust_machine_uid\",", "            r\"(?i)(machine_uid|get_machine_id)\",", "            \"Rust machine-uid crate usage\",", "            \"HIGH\","], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": ["Self", "add_pattern()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\patterns.rs", "line_number": 209, "content": "            r\"(?i)(machine_uid|get_machine_id)\",", "pattern_type": "Rust machine-uid crate usage (RUST_SPECIFIC)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 681308900}, "context_lines": ["", "        Self::add_pattern(patterns, \"rust_machine_uid\",", "            r\"(?i)(machine_uid|get_machine_id)\",", "            \"Rust machine-uid crate usage\",", "            \"HIGH\",", "            \"RUST_SPECIFIC\""], "parent_structure": "fn add_high_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\scanner.rs", "line_number": 134, "content": "                    data_flow_analysis: self.analyze_data_flow(&content, line_number),", "pattern_type": "Data flow analysis patterns (DATA_FLOW)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 719086200}, "context_lines": ["                    child_references: self.find_child_references(&content, &line_content),", "                    obfuscation_level: self.detect_obfuscation(&line_content),", "                    data_flow_analysis: self.analyze_data_flow(&content, line_number),", "                });", "            }", "        }"], "parent_structure": "pub async fn scan_file(&self, file_path: &Path) -> Result<Vec<Detection>, Box<dyn std::error::Error>> {", "child_references": ["data_flow_analysis", "analyze_data_flow()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\scanner.rs", "line_number": 235, "content": "        if current_line.contains(\"=\") && (current_line.contains(\"send\") || current_line.contains(\"transmit\")) {", "pattern_type": "Data transmission to external servers (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 745514300}, "context_lines": ["", "        // Simple data flow analysis - look for assignments and function calls", "        if current_line.contains(\"=\") && (current_line.contains(\"send\") || current_line.contains(\"transmit\")) {", "            return Some(\"DATA_TRANSMISSION\".to_string());", "        }", ""], "parent_structure": "fn analyze_data_flow(&self, content: &str, line_number: usize) -> Option<String> {", "child_references": ["contains()"], "obfuscation_level": "LOW", "data_flow_analysis": "DATA_TRANSMISSION"}, {"file_path": ".\\src\\scanner.rs", "line_number": 1, "content": "use crate::{Detection, ScanResult, TelemetryPatterns};", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 775371700}, "context_lines": ["use crate::{Detection, ScanResult, TelemetryPatterns};", "use std::collections::HashMap;", "use std::fs;", "use std::path::Path;"], "parent_structure": null, "child_references": ["crate"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\scanner.rs", "line_number": 10, "content": "    patterns: TelemetryPatterns,", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 797639800}, "context_lines": ["", "pub struct FileScanner {", "    patterns: TelemetryPatterns,", "    extensions: Vec<String>,", "    deep_scan: bool,", "    analyze_structures: bool,"], "parent_structure": "pub struct FileScanner {", "child_references": ["patterns"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\scanner.rs", "line_number": 18, "content": "    pub fn new(patterns: TelemetryPatterns, extensions: Vec<&str>) -> Self {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 820411700}, "context_lines": ["", "impl FileScanner {", "    pub fn new(patterns: TelemetryPatterns, extensions: Vec<&str>) -> Self {", "        Self {", "            patterns,", "            extensions: extensions.iter().map(|s| s.to_string()).collect(),"], "parent_structure": "pub fn new(patterns: TelemetryPatterns, extensions: Vec<&str>) -> Self {", "child_references": ["patterns", "new()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\scanner.rs", "line_number": 28, "content": "    pub fn new_advanced(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool) -> Self {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 844945300}, "context_lines": ["    }", "", "    pub fn new_advanced(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool) -> Self {", "        Self {", "            patterns,", "            extensions: extensions.iter().map(|s| s.to_string()).collect(),"], "parent_structure": "pub fn new_advanced(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool) -> Self {", "child_references": ["patterns", "new_advanced()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\scanner.rs", "line_number": 132, "content": "                    child_references: self.find_child_references(&content, &line_content),", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 869404700}, "context_lines": ["                    context_lines: self.get_context_lines(&content, line_number, 3),", "                    parent_structure: self.analyze_parent_structure(&content, line_number),", "                    child_references: self.find_child_references(&content, &line_content),", "                    obfuscation_level: self.detect_obfuscation(&line_content),", "                    data_flow_analysis: self.analyze_data_flow(&content, line_number),", "                });"], "parent_structure": "pub async fn scan_file(&self, file_path: &Path) -> Result<Vec<Detection>, Box<dyn std::error::Error>> {", "child_references": ["child_references", "find_child_references()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\scanner.rs", "line_number": 176, "content": "    fn find_child_references(&self, _content: &str, current_line: &str) -> Vec<String> {", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 895561300}, "context_lines": ["    }", "", "    fn find_child_references(&self, _content: &str, current_line: &str) -> Vec<String> {", "        if !self.analyze_structures {", "            return Vec::new();", "        }"], "parent_structure": "fn find_child_references(&self, _content: &str, current_line: &str) -> Vec<String> {", "child_references": ["_content", "find_child_references()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\scanner.rs", "line_number": 102, "content": "            scan_id: uuid::Uuid::new_v4().to_string(),", "pattern_type": "UUID generation for identification (MACHINE_ID)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 920262700}, "context_lines": ["            data_flow_map: HashMap::new(),", "            obfuscation_patterns: Vec::new(),", "            scan_id: uuid::Uuid::new_v4().to_string(),", "        })", "    }", ""], "parent_structure": "pub async fn scan_directory(&self, path: &Path) -> Result<ScanResult, Box<dyn std::error::Error>> {", "child_references": ["scan_id", "new_v4()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\scanner.rs", "line_number": 1, "content": "use crate::{Detection, ScanResult, TelemetryPatterns};", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 944540300}, "context_lines": ["use crate::{Detection, ScanResult, TelemetryPatterns};", "use std::collections::HashMap;", "use std::fs;", "use std::path::Path;"], "parent_structure": null, "child_references": ["crate"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\scanner.rs", "line_number": 10, "content": "    patterns: TelemetryPatterns,", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 965662600}, "context_lines": ["", "pub struct FileScanner {", "    patterns: TelemetryPatterns,", "    extensions: Vec<String>,", "    deep_scan: bool,", "    analyze_structures: bool,"], "parent_structure": "pub struct FileScanner {", "child_references": ["patterns"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\scanner.rs", "line_number": 18, "content": "    pub fn new(patterns: TelemetryPatterns, extensions: Vec<&str>) -> Self {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522963, "nanos_since_epoch": 993891300}, "context_lines": ["", "impl FileScanner {", "    pub fn new(patterns: TelemetryPatterns, extensions: Vec<&str>) -> Self {", "        Self {", "            patterns,", "            extensions: extensions.iter().map(|s| s.to_string()).collect(),"], "parent_structure": "pub fn new(patterns: TelemetryPatterns, extensions: Vec<&str>) -> Self {", "child_references": ["patterns", "new()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\src\\scanner.rs", "line_number": 28, "content": "    pub fn new_advanced(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool) -> Self {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 26680700}, "context_lines": ["    }", "", "    pub fn new_advanced(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool) -> Self {", "        Self {", "            patterns,", "            extensions: extensions.iter().map(|s| s.to_string()).collect(),"], "parent_structure": "pub fn new_advanced(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool) -> Self {", "child_references": ["patterns", "new_advanced()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 14, "content": "    \"machine_id_generation\": {", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 116141700}, "context_lines": ["# Telemetry and Machine ID patterns (simplified version of the Rust patterns)", "PATTERNS = {", "    \"machine_id_generation\": {", "        \"regex\": r\"(?i)(machine[_-]?id|device[_-]?id|hardware[_-]?id|system[_-]?id|unique[_-]?id)\",", "        \"description\": \"Machine/Device ID generation or usage\",", "        \"severity\": \"HIGH\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 18, "content": "        \"category\": \"MACHINE_ID\"", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 141111100}, "context_lines": ["        \"description\": \"Machine/Device ID generation or usage\",", "        \"severity\": \"HIGH\",", "        \"category\": \"MACHINE_ID\"", "    },", "    \"uuid_generation\": {", "        \"regex\": r\"(?i)(uuid::v[1-5]|Uuid::new|uuid\\.new|generate[_-]?uuid)\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 24, "content": "        \"category\": \"MACHINE_ID\"", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 166048400}, "context_lines": ["        \"description\": \"UUID generation for identification\", ", "        \"severity\": \"MEDIUM\",", "        \"category\": \"MACHINE_ID\"", "    },", "    \"mac_address\": {", "        \"regex\": r\"(?i)(mac[_-]?address|get[_-]?mac|network[_-]?interface)\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 30, "content": "        \"category\": \"MACHINE_ID\"", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 188924500}, "context_lines": ["        \"description\": \"MAC address collection\",", "        \"severity\": \"HIGH\", ", "        \"category\": \"MACHINE_ID\"", "    },", "    \"telemetry_collection\": {", "        \"regex\": r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 51, "content": "        \"regex\": r\"(?i)(machine_uid|get_machine_id)\",", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 210699700}, "context_lines": ["    },", "    \"rust_machine_uid\": {", "        \"regex\": r\"(?i)(machine_uid|get_machine_id)\",", "        \"description\": \"Rust machine-uid crate usage\", ", "        \"severity\": \"HIGH\",", "        \"category\": \"RUST_SPECIFIC\""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 39, "content": "        \"regex\": r\"(?i)(send[_-]?data|transmit|upload[_-]?stats|report[_-]?usage)\",", "pattern_type": "Data transmission to external servers (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 231975400}, "context_lines": ["    },", "    \"data_transmission\": {", "        \"regex\": r\"(?i)(send[_-]?data|transmit|upload[_-]?stats|report[_-]?usage)\",", "        \"description\": \"Data transmission to external servers\",", "        \"severity\": \"HIGH\",", "        \"category\": \"TELEMETRY\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 3, "content": "Simple Python script to test the telemetry detection patterns", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 255285600}, "context_lines": ["#!/usr/bin/env python3", "\"\"\"", "Simple Python script to test the telemetry detection patterns", "This can be used to verify the patterns work before building the Rust version", "\"\"\"", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 12, "content": "# Telemetry and Machine ID patterns (simplified version of the Rust patterns)", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 275247500}, "context_lines": ["from pathlib import Path", "", "# Telemetry and Machine ID patterns (simplified version of the Rust patterns)", "PATTERNS = {", "    \"machine_id_generation\": {", "        \"regex\": r\"(?i)(machine[_-]?id|device[_-]?id|hardware[_-]?id|system[_-]?id|unique[_-]?id)\","], "parent_structure": null, "child_references": ["patterns()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 32, "content": "    \"telemetry_collection\": {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 297544400}, "context_lines": ["        \"category\": \"MACHINE_ID\"", "    },", "    \"telemetry_collection\": {", "        \"regex\": r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "        \"description\": \"Telemetry/Analytics collection\",", "        \"severity\": \"HIGH\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\test_patterns.py", "line_number": 33, "content": "        \"regex\": r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 317911900}, "context_lines": ["    },", "    \"telemetry_collection\": {", "        \"regex\": r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "        \"description\": \"Telemetry/Analytics collection\",", "        \"severity\": \"HIGH\",", "        \"category\": \"TELEMETRY\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 34, "content": "        \"description\": \"Telemetry/Analytics collection\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 339593000}, "context_lines": ["    \"telemetry_collection\": {", "        \"regex\": r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "        \"description\": \"Telemetry/Analytics collection\",", "        \"severity\": \"HIGH\",", "        \"category\": \"TELEMETRY\"", "    },"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\test_patterns.py", "line_number": 36, "content": "        \"category\": \"TELEMETRY\"", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 360014600}, "context_lines": ["        \"description\": \"Telemetry/Analytics collection\",", "        \"severity\": \"HIGH\",", "        \"category\": \"TELEMETRY\"", "    },", "    \"data_transmission\": {", "        \"regex\": r\"(?i)(send[_-]?data|transmit|upload[_-]?stats|report[_-]?usage)\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 42, "content": "        \"category\": \"TELEMETRY\"", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 380030300}, "context_lines": ["        \"description\": \"Data transmission to external servers\",", "        \"severity\": \"HIGH\",", "        \"category\": \"TELEMETRY\"", "    },", "    \"rust_sysinfo\": {", "        \"regex\": r\"(?i)(sysinfo::|System::new|get_processor|get_networks)\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 59, "content": "    \"\"\"Scan a single file for telemetry patterns\"\"\"", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 400118500}, "context_lines": ["", "def scan_file(file_path):", "    \"\"\"Scan a single file for telemetry patterns\"\"\"", "    try:", "        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:", "            content = f.read()"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 87, "content": "    \"\"\"Scan directory for files with telemetry patterns\"\"\"", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 420465000}, "context_lines": ["", "def scan_directory(directory, extensions=None):", "    \"\"\"Scan directory for files with telemetry patterns\"\"\"", "    if extensions is None:", "        extensions = ['.rs', '.py', '.js', '.ts', '.cpp', '.c', '.h', '.cs', '.java', '.go']", "    "], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 118, "content": "        print(\"✅ No telemetry or machine ID patterns detected!\")", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 440180200}, "context_lines": ["    ", "    if not detections:", "        print(\"✅ No telemetry or machine ID patterns detected!\")", "        return", "    ", "    print(f\"\\n🚨 DETECTIONS:\")"], "parent_structure": null, "child_references": ["print()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 151, "content": "    print(\"🔍 Telemetry & Machine ID Pattern Tester\")", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 462284000}, "context_lines": ["            scan_path = \".\"", "    ", "    print(\"🔍 Telemetry & Machine ID Pattern Tester\")", "    print(\"=\" * 50)", "    ", "    if os.path.isfile(scan_path):"], "parent_structure": null, "child_references": ["print()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 45, "content": "        \"regex\": r\"(?i)(sysinfo::|System::new|get_processor|get_networks)\",", "pattern_type": "Rust sysinfo crate usage (RUST_SPECIFIC)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 483974200}, "context_lines": ["    },", "    \"rust_sysinfo\": {", "        \"regex\": r\"(?i)(sysinfo::|System::new|get_processor|get_networks)\",", "        \"description\": \"Rust sysinfo crate usage\",", "        \"severity\": \"HIGH\",", "        \"category\": \"RUST_SPECIFIC\""], "parent_structure": null, "child_references": ["sysinfo"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 21, "content": "        \"regex\": r\"(?i)(uuid::v[1-5]|Uuid::new|uuid\\.new|generate[_-]?uuid)\",", "pattern_type": "UUID generation for identification (MACHINE_ID)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 507771400}, "context_lines": ["    },", "    \"uuid_generation\": {", "        \"regex\": r\"(?i)(uuid::v[1-5]|Uuid::new|uuid\\.new|generate[_-]?uuid)\",", "        \"description\": \"UUID generation for identification\", ", "        \"severity\": \"MEDIUM\",", "        \"category\": \"MACHINE_ID\""], "parent_structure": null, "child_references": ["uuid"], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 26, "content": "    \"mac_address\": {", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 530878100}, "context_lines": ["        \"category\": \"MACHINE_ID\"", "    },", "    \"mac_address\": {", "        \"regex\": r\"(?i)(mac[_-]?address|get[_-]?mac|network[_-]?interface)\",", "        \"description\": \"MAC address collection\",", "        \"severity\": \"HIGH\", "], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 51, "content": "        \"regex\": r\"(?i)(machine_uid|get_machine_id)\",", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 551116800}, "context_lines": ["    },", "    \"rust_machine_uid\": {", "        \"regex\": r\"(?i)(machine_uid|get_machine_id)\",", "        \"description\": \"Rust machine-uid crate usage\", ", "        \"severity\": \"HIGH\",", "        \"category\": \"RUST_SPECIFIC\""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 3, "content": "Simple Python script to test the telemetry detection patterns", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 573128500}, "context_lines": ["#!/usr/bin/env python3", "\"\"\"", "Simple Python script to test the telemetry detection patterns", "This can be used to verify the patterns work before building the Rust version", "\"\"\"", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 12, "content": "# Telemetry and Machine ID patterns (simplified version of the Rust patterns)", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 592288300}, "context_lines": ["from pathlib import Path", "", "# Telemetry and Machine ID patterns (simplified version of the Rust patterns)", "PATTERNS = {", "    \"machine_id_generation\": {", "        \"regex\": r\"(?i)(machine[_-]?id|device[_-]?id|hardware[_-]?id|system[_-]?id|unique[_-]?id)\","], "parent_structure": null, "child_references": ["patterns()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 32, "content": "    \"telemetry_collection\": {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 613748500}, "context_lines": ["        \"category\": \"MACHINE_ID\"", "    },", "    \"telemetry_collection\": {", "        \"regex\": r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "        \"description\": \"Telemetry/Analytics collection\",", "        \"severity\": \"HIGH\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\test_patterns.py", "line_number": 33, "content": "        \"regex\": r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 634035700}, "context_lines": ["    },", "    \"telemetry_collection\": {", "        \"regex\": r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "        \"description\": \"Telemetry/Analytics collection\",", "        \"severity\": \"HIGH\",", "        \"category\": \"TELEMETRY\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 34, "content": "        \"description\": \"Telemetry/Analytics collection\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 655348500}, "context_lines": ["    \"telemetry_collection\": {", "        \"regex\": r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "        \"description\": \"Telemetry/Analytics collection\",", "        \"severity\": \"HIGH\",", "        \"category\": \"TELEMETRY\"", "    },"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": "DATA_COLLECTION"}, {"file_path": ".\\test_patterns.py", "line_number": 36, "content": "        \"category\": \"TELEMETRY\"", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 675986900}, "context_lines": ["        \"description\": \"Telemetry/Analytics collection\",", "        \"severity\": \"HIGH\",", "        \"category\": \"TELEMETRY\"", "    },", "    \"data_transmission\": {", "        \"regex\": r\"(?i)(send[_-]?data|transmit|upload[_-]?stats|report[_-]?usage)\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 42, "content": "        \"category\": \"TELEMETRY\"", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 695600700}, "context_lines": ["        \"description\": \"Data transmission to external servers\",", "        \"severity\": \"HIGH\",", "        \"category\": \"TELEMETRY\"", "    },", "    \"rust_sysinfo\": {", "        \"regex\": r\"(?i)(sysinfo::|System::new|get_processor|get_networks)\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 59, "content": "    \"\"\"Scan a single file for telemetry patterns\"\"\"", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 715310500}, "context_lines": ["", "def scan_file(file_path):", "    \"\"\"Scan a single file for telemetry patterns\"\"\"", "    try:", "        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:", "            content = f.read()"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 68, "content": "            regex = re.compile(pattern_info[\"regex\"])", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 737725000}, "context_lines": ["        ", "        for pattern_name, pattern_info in PATTERNS.items():", "            regex = re.compile(pattern_info[\"regex\"])", "            ", "            for line_num, line in enumerate(lines, 1):", "                if regex.search(line):"], "parent_structure": null, "child_references": ["regex", "compile()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 87, "content": "    \"\"\"Scan directory for files with telemetry patterns\"\"\"", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 771547100}, "context_lines": ["", "def scan_directory(directory, extensions=None):", "    \"\"\"Scan directory for files with telemetry patterns\"\"\"", "    if extensions is None:", "        extensions = ['.rs', '.py', '.js', '.ts', '.cpp', '.c', '.h', '.cs', '.java', '.go']", "    "], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 118, "content": "        print(\"✅ No telemetry or machine ID patterns detected!\")", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 799767400}, "context_lines": ["    ", "    if not detections:", "        print(\"✅ No telemetry or machine ID patterns detected!\")", "        return", "    ", "    print(f\"\\n🚨 DETECTIONS:\")"], "parent_structure": null, "child_references": ["print()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 151, "content": "    print(\"🔍 Telemetry & Machine ID Pattern Tester\")", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 832719600}, "context_lines": ["            scan_path = \".\"", "    ", "    print(\"🔍 Telemetry & Machine ID Pattern Tester\")", "    print(\"=\" * 50)", "    ", "    if os.path.isfile(scan_path):"], "parent_structure": null, "child_references": ["print()"], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 50, "content": "    \"rust_machine_uid\": {", "pattern_type": "Rust machine-uid crate usage (RUST_SPECIFIC)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 864428300}, "context_lines": ["        \"category\": \"RUST_SPECIFIC\"", "    },", "    \"rust_machine_uid\": {", "        \"regex\": r\"(?i)(machine_uid|get_machine_id)\",", "        \"description\": \"Rust machine-uid crate usage\", ", "        \"severity\": \"HIGH\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}, {"file_path": ".\\test_patterns.py", "line_number": 51, "content": "        \"regex\": r\"(?i)(machine_uid|get_machine_id)\",", "pattern_type": "Rust machine-uid crate usage (RUST_SPECIFIC)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751522964, "nanos_since_epoch": 901063200}, "context_lines": ["    },", "    \"rust_machine_uid\": {", "        \"regex\": r\"(?i)(machine_uid|get_machine_id)\",", "        \"description\": \"Rust machine-uid crate usage\", ", "        \"severity\": \"HIGH\",", "        \"category\": \"RUST_SPECIFIC\""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null}], "scan_time": {"secs_since_epoch": 1751522957, "nanos_since_epoch": 611345800}, "files_scanned": 7, "directories_scanned": 255, "penetration_level": "high", "structure_analysis": {}, "data_flow_map": {}, "obfuscation_patterns": [], "scan_id": "f8103a9b-82f5-41ec-a515-6ec02c139d57"}