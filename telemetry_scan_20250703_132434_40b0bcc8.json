{"detections": [{"file_path": "src\\fingerprint.rs", "line_number": 64, "content": "        let salt = \"telemetry-scanner-v1\";", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 281273400}, "context_lines": ["        ", "        // Hash with salt for privacy", "        let salt = \"telemetry-scanner-v1\";", "        let mut hasher = Sha256::new();", "        hasher.update(salt.as_bytes());", "        hasher.update(joined.as_bytes());"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.6204537136927954, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.6204537136927954, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 243, "content": "    Medium,     // Possible machine ID or tracking token", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 287987700}, "context_lines": ["    Critical,   // Raw hardware ID being transmitted", "    High,       // Likely machine ID with high entropy", "    Medium,     // Possible machine ID or tracking token", "    Low,        // Low entropy or clearly hashed", "}", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.019620387442341, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.019620387442341, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 194, "content": "        is_likely_machine_id: false,", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 294564300}, "context_lines": ["pub fn analyze_id_string(value: &str) -> IdAnalysis {", "    let mut analysis = IdAnalysis {", "        is_likely_machine_id: false,", "        entropy_score: 0.0,", "        format_type: IdFormat::Unknown,", "        privacy_risk: PrivacyRisk::Low,"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.647869792568111, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.647869792568111, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 208, "content": "    analysis.is_likely_machine_id = is_likely_machine_id(value, &analysis.format_type);", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 301083700}, "context_lines": ["", "    // Check if it's likely a machine ID", "    analysis.is_likely_machine_id = is_likely_machine_id(value, &analysis.format_type);", "", "    // Check if it appears to be hashed", "    analysis.is_hashed = appears_hashed(value);"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.333083584385079, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.333083584385079, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 221, "content": "    pub is_likely_machine_id: bool,", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 307950900}, "context_lines": ["#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]", "pub struct IdAnalysis {", "    pub is_likely_machine_id: bool,", "    pub entropy_score: f64,", "    pub format_type: IdFormat,", "    pub privacy_risk: PrivacyRisk,"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.014438730983426, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.014438730983426, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 312, "content": "fn is_likely_machine_id(value: &str, format: &IdFormat) -> bool {", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 315016900}, "context_lines": ["}", "", "fn is_likely_machine_id(value: &str, format: &IdFormat) -> bool {", "    match format {", "        IdFormat::Uuid => true,", "        IdFormat::MacAddress => true,"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.677353628948138, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.677353628948138, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 352, "content": "    if analysis.is_likely_machine_id && analysis.entropy_score > 3.0 {", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 322278400}, "context_lines": ["", "    // High entropy + machine ID format = high risk", "    if analysis.is_likely_machine_id && analysis.entropy_score > 3.0 {", "        match analysis.format_type {", "            IdFormat::MacAddress => PrivacyRisk::Critical, // Raw MAC is critical", "            IdFormat::Uuid => PrivacyRisk::High,           // UUID could be machine-specific"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.232924897170621, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.232924897170621, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 359, "content": "    } else if analysis.is_likely_machine_id {", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 328859700}, "context_lines": ["            _ => PrivacyRisk::Medium,", "        }", "    } else if analysis.is_likely_machine_id {", "        PrivacyRisk::Medium", "    } else {", "        PrivacyRisk::Low"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.780307540581812, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.780307540581812, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 30, "content": "        let cpu_brand = sys.global_cpu_info().brand();", "pattern_type": "CPU information collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 335313700}, "context_lines": ["", "        // 1. CPU information (brand, architecture)", "        let cpu_brand = sys.global_cpu_info().brand();", "        if !cpu_brand.is_empty() {", "            parts.push(format!(\"cpu:{}\", cpu_brand));", "        }"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "cpu_info", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.239550737055551, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.239550737055551, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 1, "content": "use sha2::{Digest, Sha256};", "pattern_type": "Hash function usage (good for privacy) (PRIVACY_PROTECTION)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 342461600}, "context_lines": ["use sha2::{Digest, Sha256};", "use sysinfo::System;", "use std::collections::HashMap;", "use serde::{Serialize, Deserialize};"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.134336113194451, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.134336113194451, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 65, "content": "        let mut hasher = Sha256::new();", "pattern_type": "Hash function usage (good for privacy) (PRIVACY_PROTECTION)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 351304800}, "context_lines": ["        // Hash with salt for privacy", "        let salt = \"telemetry-scanner-v1\";", "        let mut hasher = Sha256::new();", "        hasher.update(salt.as_bytes());", "        hasher.update(joined.as_bytes());", "        let fingerprint = format!(\"{:x}\", hasher.finalize());"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.7846502954524834, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.7846502954524834, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 328, "content": "    // SHA-256 (64 hex chars), SHA-1 (40 hex chars), MD5 (32 hex chars)", "pattern_type": "Hash function usage (good for privacy) (PRIVACY_PROTECTION)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 359158900}, "context_lines": ["    let clean_value = value.trim();", "    ", "    // SHA-256 (64 hex chars), SHA-1 (40 hex chars), MD5 (32 hex chars)", "    if regex::Regex::new(r\"^[0-9a-fA-F]{32}$|^[0-9a-fA-F]{40}$|^[0-9a-fA-F]{64}$\")", "        .unwrap()", "        .is_match(clean_value) {"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.212439020608419, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.212439020608419, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 41, "content": "        if let Some(disk_serial) = Self::get_primary_disk_serial(&sys) {", "pattern_type": "Disk/Drive serial number collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 365709900}, "context_lines": ["", "        // 3. First non-removable disk serial", "        if let Some(disk_serial) = Self::get_primary_disk_serial(&sys) {", "            parts.push(format!(\"disk:{}\", disk_serial));", "        }", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "disk_serial", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.178398211845388, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.178398211845388, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 42, "content": "            parts.push(format!(\"disk:{}\", disk_serial));", "pattern_type": "Disk/Drive serial number collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 373603100}, "context_lines": ["        // 3. First non-removable disk serial", "        if let Some(disk_serial) = Self::get_primary_disk_serial(&sys) {", "            parts.push(format!(\"disk:{}\", disk_serial));", "        }", "", "        // 4. MAC address of primary network interface"], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "disk_serial", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.236283148544009, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.236283148544009, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 149, "content": "    fn get_primary_disk_serial(_sys: &System) -> Option<String> {", "pattern_type": "Disk/Drive serial number collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 379096800}, "context_lines": ["    }", "", "    fn get_primary_disk_serial(_sys: &System) -> Option<String> {", "        // Note: sysinfo 0.30 changed disk API, using placeholder for now", "        // In production, would use platform-specific disk enumeration", "        Some(\"disk_placeholder\".to_string())"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "disk_serial", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.4529329135154585, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.4529329135154585, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 128, "content": "        // On Windows, try WMI query for system UUID", "pattern_type": "WMI queries for system information (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 384417100}, "context_lines": ["    #[cfg(target_os = \"windows\")]", "    fn get_windows_system_uuid() -> Option<String> {", "        // On Windows, try WMI query for system UUID", "        // For now, return None - can be enhanced with winapi crate", "        None", "    }"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "system_information", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.8876577468624287, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.8876577468624287, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 77, "content": "    /// Get the hashed fingerprint (safe to transmit)", "pattern_type": "Data transmission to external servers (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 391355200}, "context_lines": ["    }", "", "    /// Get the hashed fingerprint (safe to transmit)", "    pub fn fingerprint(&self) -> &str {", "        &self.fingerprint", "    }"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.8324982696779486, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.8324982696779486, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 241, "content": "    Critical,   // Raw hardware ID being transmitted", "pattern_type": "Data transmission to external servers (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 398778700}, "context_lines": ["#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Serialize, Deserialize)]", "pub enum PrivacyRisk {", "    Critical,   // Raw hardware ID being transmitted", "    High,       // Likely machine ID with high entropy", "    Medium,     // Possible machine ID or tracking token", "    Low,        // Low entropy or clearly hashed"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.94305360304005, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.94305360304005, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 10, "content": "pub struct MachineFingerprint {", "pattern_type": "Complex data structures for tracking (STRUCTURE)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 404741900}, "context_lines": ["/// This module creates deterministic, non-reversible machine fingerprints", "/// by hashing hardware identifiers. Raw hardware data never leaves the host.", "pub struct MachineFingerprint {", "    fingerprint: String,", "    components: Vec<String>,", "    entropy_score: f64,"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.9292144551992183, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.9292144551992183, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 46, "content": "        if let Some(mac_addr) = Self::get_primary_mac_address() {", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 411647900}, "context_lines": ["", "        // 4. MAC address of primary network interface", "        if let Some(mac_addr) = Self::get_primary_mac_address() {", "            parts.push(format!(\"mac:{}\", mac_addr));", "        }", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.125060162494488, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.125060162494488, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 118, "content": "            Self::get_macos_system_uuid()", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 417129000}, "context_lines": ["        #[cfg(target_os = \"macos\")]", "        {", "            Self::get_macos_system_uuid()", "        }", "        #[cfg(not(any(target_os = \"windows\", target_os = \"linux\", target_os = \"macos\")))]", "        {"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.7652541383950533, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.7652541383950533, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 143, "content": "    fn get_macos_system_uuid() -> Option<String> {", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 426509600}, "context_lines": ["", "    #[cfg(target_os = \"macos\")]", "    fn get_macos_system_uuid() -> Option<String> {", "        // On macOS, could use IOKit to get hardware UUID", "        // For now, return None - can be enhanced with system_profiler", "        None"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.3834651896016465, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.3834651896016465, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 155, "content": "    fn get_primary_mac_address() -> Option<String> {", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 433156600}, "context_lines": ["    }", "", "    fn get_primary_mac_address() -> Option<String> {", "        match mac_address::get_mac_address() {", "            Ok(Some(mac)) => Some(mac.to_string()),", "            _ => None,"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.358623612163834, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.358623612163834, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 156, "content": "        match mac_address::get_mac_address() {", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 439787800}, "context_lines": ["", "    fn get_primary_mac_address() -> Option<String> {", "        match mac_address::get_mac_address() {", "            Ok(Some(mac)) => Some(mac.to_string()),", "            _ => None,", "        }"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.657290837970832, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.657290837970832, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 231, "content": "    MacAddress,     // MAC address format", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 445682500}, "context_lines": ["pub enum IdFormat {", "    Uuid,           // UUID format (8-4-4-4-12)", "    MacAddress,     // MAC address format", "    HexString,      // Long hex string", "    Base64,         // Base64 encoded", "    Numeric,        // Pure numeric"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.4909831018624544, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.4909831018624544, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 282, "content": "        return IdFormat::<PERSON><PERSON><PERSON><PERSON>;", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 450730700}, "context_lines": ["        .unwrap()", "        .is_match(clean_value) {", "        return IdFormat::<PERSON><PERSON><PERSON><PERSON>;", "    }", "", "    // Long hex string (likely hash)"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.7453635426883034, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.7453635426883034, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 315, "content": "        IdFormat::MacAddress => true,", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 455790300}, "context_lines": ["    match format {", "        IdFormat::Uuid => true,", "        IdFormat::MacAddress => true,", "        IdFormat::HexString => value.len() >= 32, // Likely hash or hardware serial", "        IdFormat::Base64 => value.len() >= 20,    // Likely encoded ID", "        IdFormat::Numeric => value.len() >= 10,   // Long numeric could be serial"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.7843437452721784, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.7843437452721784, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 354, "content": "            IdFormat::MacAddress => PrivacyRisk::Critical, // Raw MAC is critical", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 460551700}, "context_lines": ["    if analysis.is_likely_machine_id && analysis.entropy_score > 3.0 {", "        match analysis.format_type {", "            IdFormat::MacAddress => PrivacyRisk::Critical, // Raw MAC is critical", "            IdFormat::Uuid => PrivacyRisk::High,           // UUID could be machine-specific", "            IdFormat::HexString => PrivacyRisk::Medium,    // Could be serial number", "            _ => PrivacyRisk::Medium,"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.159591506991306, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.159591506991306, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 2, "content": "use sysinfo::System;", "pattern_type": "Rust sysinfo crate usage (RUST_SPECIFIC)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 465602700}, "context_lines": ["use sha2::{Digest, Sha256};", "use sysinfo::System;", "use std::collections::HashMap;", "use serde::{Serialize, Deserialize};", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.6219280948873624, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.6219280948873624, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 26, "content": "        let mut sys = System::new_all();", "pattern_type": "Rust sysinfo crate usage (RUST_SPECIFIC)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 472054000}, "context_lines": ["    pub fn generate() -> Self {", "        let mut parts: Vec<String> = Vec::new();", "        let mut sys = System::new_all();", "        sys.refresh_all();", "", "        // 1. CPU information (brand, architecture)"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.620950594454668, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.620950594454668, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 64, "content": "        let salt = \"telemetry-scanner-v1\";", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 477444400}, "context_lines": ["        ", "        // Hash with salt for privacy", "        let salt = \"telemetry-scanner-v1\";", "        let mut hasher = Sha256::new();", "        hasher.update(salt.as_bytes());", "        hasher.update(joined.as_bytes());"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.6204537136927946, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.6204537136927946, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 84, "content": "        self.components.len()", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 482802900}, "context_lines": ["    /// Get component count (for debugging)", "    pub fn component_count(&self) -> usize {", "        self.components.len()", "    }", "", "    /// Get entropy score (higher = more unique)"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.42660944325423, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.42660944325423, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 94, "content": "        self.entropy_score > 2.0 && self.component_count() >= 3", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 488398300}, "context_lines": ["    /// Check if fingerprint has sufficient entropy for uniqueness", "    pub fn has_sufficient_entropy(&self) -> bool {", "        self.entropy_score > 2.0 && self.component_count() >= 3", "    }", "", "    /// Get component types (without raw values)"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.111537068478741, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.111537068478741, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 99, "content": "        self.components.iter()", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 493500700}, "context_lines": ["    /// Get component types (without raw values)", "    pub fn component_types(&self) -> Vec<String> {", "        self.components.iter()", "            .map(|c| c.split(':').next().unwrap_or(\"unknown\").to_string())", "            .collect()", "    }"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.6150610122030695, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.6150610122030695, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 243, "content": "    Medium,     // Possible machine ID or tracking token", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 500592100}, "context_lines": ["    Critical,   // Raw hardware ID being transmitted", "    High,       // Likely machine ID with high entropy", "    Medium,     // Possible machine ID or tracking token", "    Low,        // Low entropy or clearly hashed", "}", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.019620387442342, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.019620387442342, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 9, "content": "/// by hashing hardware identifiers. Raw hardware data never leaves the host.", "pattern_type": "Obfuscated ID generation/processing (OBFUSCATED)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 507839200}, "context_lines": ["/// ", "/// This module creates deterministic, non-reversible machine fingerprints", "/// by hashing hardware identifiers. Raw hardware data never leaves the host.", "pub struct MachineFingerprint {", "    fingerprint: String,", "    components: Vec<String>,"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.975921471264821, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.975921471264821, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\fingerprint.rs", "line_number": 317, "content": "        IdFormat::Base64 => value.len() >= 20,    // Likely encoded ID", "pattern_type": "Obfuscated ID generation/processing (OBFUSCATED)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 513564400}, "context_lines": ["        IdFormat::MacAddress => true,", "        IdFormat::HexString => value.len() >= 32, // Likely hash or hardware serial", "        IdFormat::Base64 => value.len() >= 20,    // Likely encoded ID", "        IdFormat::Numeric => value.len() >= 10,   // Long numeric could be serial", "        IdFormat::Mixed => value.len() >= 12,     // Mixed format with sufficient length", "        IdFormat::Unknown => false,"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.350925013141459, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.350925013141459, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 146, "content": "    let auto_save_filename = format!(\"telemetry_scan_{}_{}.json\",", "pattern_type": "Configuration file access (FILESYSTEM)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 543459200}, "context_lines": ["    // Generate unique datetime filename for auto-save", "    let now: DateTime<Local> = Local::now();", "    let auto_save_filename = format!(\"telemetry_scan_{}_{}.json\",", "        now.format(\"%Y%m%d_%H%M%S\"),", "        uuid::Uuid::new_v4().to_string()[..8].to_string()", "    );"], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.3622704615986985, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.3622704615986985, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 52, "content": "    pub machine_fingerprint: String,", "pattern_type": "Local fingerprint generation (monitor for transmission) (LOCAL_FINGERPRINT)", "severity": "CRITICAL", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 548418500}, "context_lines": ["    pub obfuscation_patterns: Vec<String>,", "    pub scan_id: String,", "    pub machine_fingerprint: String,", "    pub fingerprint_entropy: f64,", "    pub privacy_summary: PrivacySummary,", "}"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.9403510429286888, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.9403510429286888, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 16, "content": "use patterns::TelemetryPatterns;", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 553341900}, "context_lines": ["mod fingerprint;", "", "use patterns::TelemetryPatterns;", "use scanner::FileScanner;", "use watcher::FileWatcher;", "use fingerprint::{MachineFingerprint, IdAnalysis};"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.6678377974034158, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.6678377974034158, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 70, "content": "    let matches = Command::new(\"Advanced Telemetry & Machine ID Watcher\")", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 558014500}, "context_lines": ["#[tokio::main]", "async fn main() -> Result<(), Box<dyn std::error::Error>> {", "    let matches = Command::new(\"Advanced Telemetry & Machine ID Watcher\")", "        .version(\"2.0\")", "        .author(\"Advanced Security Scanner\")", "        .about(\"High-penetration scanner for telemetry, machine ID, and complex data structures\")"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.372450716408805, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.372450716408805, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 73, "content": "        .about(\"High-penetration scanner for telemetry, machine ID, and complex data structures\")", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 562863600}, "context_lines": ["        .version(\"2.0\")", "        .author(\"Advanced Security Scanner\")", "        .about(\"High-penetration scanner for telemetry, machine ID, and complex data structures\")", "        .arg(", "            Arg::new(\"path\")", "                .short('p')"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.334894514496948, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.334894514496948, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 146, "content": "    let auto_save_filename = format!(\"telemetry_scan_{}_{}.json\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 567458900}, "context_lines": ["    // Generate unique datetime filename for auto-save", "    let now: DateTime<Local> = Local::now();", "    let auto_save_filename = format!(\"telemetry_scan_{}_{}.json\",", "        now.format(\"%Y%m%d_%H%M%S\"),", "        uuid::Uuid::new_v4().to_string()[..8].to_string()", "    );"], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.362270461598698, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.362270461598698, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 151, "content": "    println!(\"{}\", \"🔍 Advanced Telemetry & Machine ID Watcher\".bright_cyan().bold());", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 572420000}, "context_lines": ["    );", "", "    println!(\"{}\", \"🔍 Advanced Telemetry & Machine ID Watcher\".bright_cyan().bold());", "    println!(\"Scanning path: {}\", scan_path.bright_yellow());", "    println!(\"Extensions: {}\", extensions.join(\", \").bright_green());", "    println!(\"Penetration level: {}\", penetration_level.bright_magenta());"], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.651695446447904, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.651695446447904, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 176, "content": "    let patterns = TelemetryPatterns::new_advanced(penetration_level);", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 577398600}, "context_lines": ["    }", "", "    let patterns = TelemetryPatterns::new_advanced(penetration_level);", "    let scanner = FileScanner::new_with_privacy(patterns, extensions, deep_scan, analyze_structures, privacy_analysis);", "", "    // Initial scan"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.10370516063324, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.10370516063324, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 31, "content": "    pub child_references: Vec<String>,", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 582377800}, "context_lines": ["    pub context_lines: Vec<String>,", "    pub parent_structure: Option<String>,", "    pub child_references: Vec<String>,", "    pub obfuscation_level: String,", "    pub data_flow_analysis: Option<String>,", "    pub data_collected: Option<String>,"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.1787382902043575, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.1787382902043575, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 148, "content": "        uuid::Uuid::new_v4().to_string()[..8].to_string()", "pattern_type": "UUID generation for identification (MACHINE_ID)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 587348100}, "context_lines": ["    let auto_save_filename = format!(\"telemetry_scan_{}_{}.json\",", "        now.format(\"%Y%m%d_%H%M%S\"),", "        uuid::Uuid::new_v4().to_string()[..8].to_string()", "    );", "", "    println!(\"{}\", \"🔍 Advanced Telemetry & Machine ID Watcher\".bright_cyan().bold());"], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "uuid", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.257899882396016, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.257899882396016, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 16, "content": "use patterns::TelemetryPatterns;", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 592288000}, "context_lines": ["mod fingerprint;", "", "use patterns::TelemetryPatterns;", "use scanner::FileScanner;", "use watcher::FileWatcher;", "use fingerprint::{MachineFingerprint, IdAnalysis};"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.6678377974034158, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.6678377974034158, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 70, "content": "    let matches = Command::new(\"Advanced Telemetry & Machine ID Watcher\")", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 597079800}, "context_lines": ["#[tokio::main]", "async fn main() -> Result<(), Box<dyn std::error::Error>> {", "    let matches = Command::new(\"Advanced Telemetry & Machine ID Watcher\")", "        .version(\"2.0\")", "        .author(\"Advanced Security Scanner\")", "        .about(\"High-penetration scanner for telemetry, machine ID, and complex data structures\")"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.372450716408805, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.372450716408805, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 73, "content": "        .about(\"High-penetration scanner for telemetry, machine ID, and complex data structures\")", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 601778000}, "context_lines": ["        .version(\"2.0\")", "        .author(\"Advanced Security Scanner\")", "        .about(\"High-penetration scanner for telemetry, machine ID, and complex data structures\")", "        .arg(", "            Arg::new(\"path\")", "                .short('p')"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.3348945144969475, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.3348945144969475, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 146, "content": "    let auto_save_filename = format!(\"telemetry_scan_{}_{}.json\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 606602100}, "context_lines": ["    // Generate unique datetime filename for auto-save", "    let now: DateTime<Local> = Local::now();", "    let auto_save_filename = format!(\"telemetry_scan_{}_{}.json\",", "        now.format(\"%Y%m%d_%H%M%S\"),", "        uuid::Uuid::new_v4().to_string()[..8].to_string()", "    );"], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.3622704615986985, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.3622704615986985, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 151, "content": "    println!(\"{}\", \"🔍 Advanced Telemetry & Machine ID Watcher\".bright_cyan().bold());", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 611252500}, "context_lines": ["    );", "", "    println!(\"{}\", \"🔍 Advanced Telemetry & Machine ID Watcher\".bright_cyan().bold());", "    println!(\"Scanning path: {}\", scan_path.bright_yellow());", "    println!(\"Extensions: {}\", extensions.join(\", \").bright_green());", "    println!(\"Penetration level: {}\", penetration_level.bright_magenta());"], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.651695446447903, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.651695446447903, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 163, "content": "        println!(\"Components: {} detected\", machine_fp.component_count().to_string().bright_yellow());", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 615782200}, "context_lines": ["        println!(\"\\n{}\", \"🔐 MACHINE FINGERPRINT (DEBUG)\".bright_magenta().bold());", "        println!(\"Fingerprint: {}\", machine_fp.fingerprint().bright_cyan());", "        println!(\"Components: {} detected\", machine_fp.component_count().to_string().bright_yellow());", "        println!(\"Component types: {}\", machine_fp.component_types().join(\", \").bright_green());", "        println!(\"Entropy score: {:.2}\", machine_fp.entropy_score().to_string().bright_white());", "        println!(\"Sufficient entropy: {}\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.599534001300592, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.599534001300592, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 164, "content": "        println!(\"Component types: {}\", machine_fp.component_types().join(\", \").bright_green());", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 620467900}, "context_lines": ["        println!(\"Fingerprint: {}\", machine_fp.fingerprint().bright_cyan());", "        println!(\"Components: {} detected\", machine_fp.component_count().to_string().bright_yellow());", "        println!(\"Component types: {}\", machine_fp.component_types().join(\", \").bright_green());", "        println!(\"Entropy score: {:.2}\", machine_fp.entropy_score().to_string().bright_white());", "        println!(\"Sufficient entropy: {}\",", "            if machine_fp.has_sufficient_entropy() {"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.534451042723423, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.534451042723423, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 176, "content": "    let patterns = TelemetryPatterns::new_advanced(penetration_level);", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 626023200}, "context_lines": ["    }", "", "    let patterns = TelemetryPatterns::new_advanced(penetration_level);", "    let scanner = FileScanner::new_with_privacy(patterns, extensions, deep_scan, analyze_structures, privacy_analysis);", "", "    // Initial scan"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.103705160633239, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.103705160633239, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 64, "content": "    pub hashed_ids_detected: usize,", "pattern_type": "Obfuscated ID generation/processing (OBFUSCATED)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 630926800}, "context_lines": ["    pub low_risks: usize,", "    pub raw_ids_detected: usize,", "    pub hashed_ids_detected: usize,", "    pub fingerprint_transmissions: usize,", "}", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.704302931775616, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.704302931775616, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\main.rs", "line_number": 212, "content": "    println!(\"Hashed IDs detected: {}\", result.privacy_summary.hashed_ids_detected.to_string().bright_green());", "pattern_type": "Obfuscated ID generation/processing (OBFUSCATED)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 635562600}, "context_lines": ["    println!(\"Low privacy risks: {}\", result.privacy_summary.low_risks.to_string().bright_green());", "    println!(\"Raw IDs detected: {}\", result.privacy_summary.raw_ids_detected.to_string().bright_red());", "    println!(\"Hashed IDs detected: {}\", result.privacy_summary.hashed_ids_detected.to_string().bright_green());", "    println!(\"Potential fingerprint transmissions: {}\", result.privacy_summary.fingerprint_transmissions.to_string().bright_red().bold());", "", "    if !result.detections.is_empty() {"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.6703045407759785, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.6703045407759785, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 187, "content": "            r\"(?i)(users/|\\\\users\\\\|home/|appdata|documents|desktop)\",", "pattern_type": "User directory access (FILESYSTEM)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 665650500}, "context_lines": ["        // File system patterns", "        Self::add_pattern(patterns, \"user_directories\",", "            r\"(?i)(users/|\\\\users\\\\|home/|appdata|documents|desktop)\",", "            \"User directory access\",", "            \"MEDIUM\",", "            \"FILESYSTEM\""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.228814181559269, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.228814181559269, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 194, "content": "            r\"(?i)(\\.config|\\.settings|\\.ini|\\.json|\\.xml|\\.plist)\",", "pattern_type": "Configuration file access (FILESYSTEM)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 671530600}, "context_lines": ["", "        Self::add_pattern(patterns, \"config_files\",", "            r\"(?i)(\\.config|\\.settings|\\.ini|\\.json|\\.xml|\\.plist)\",", "            \"Configuration file access\",", "            \"LOW\",", "            \"FILESYSTEM\""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.118148091276295, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.118148091276295, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 201, "content": "        Self::add_pattern(patterns, \"local_fingerprint\",", "pattern_type": "Local fingerprint generation (monitor for transmission) (LOCAL_FINGERPRINT)", "severity": "CRITICAL", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 676940900}, "context_lines": ["", "        // Fingerprint-specific patterns", "        Self::add_pattern(patterns, \"local_fingerprint\",", "            r\"(?i)(machine_fingerprint|generate_fingerprint|local[_-]?fingerprint)\",", "            \"Local fingerprint generation (monitor for transmission)\",", "            \"CRITICAL\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.099341698990737, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.099341698990737, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 202, "content": "            r\"(?i)(machine_fingerprint|generate_fingerprint|local[_-]?fingerprint)\",", "pattern_type": "Local fingerprint generation (monitor for transmission) (LOCAL_FINGERPRINT)", "severity": "CRITICAL", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 681674100}, "context_lines": ["        // Fingerprint-specific patterns", "        Self::add_pattern(patterns, \"local_fingerprint\",", "            r\"(?i)(machine_fingerprint|generate_fingerprint|local[_-]?fingerprint)\",", "            \"Local fingerprint generation (monitor for transmission)\",", "            \"CRITICAL\",", "            \"LOCAL_FINGERPRINT\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.205524750496487, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.205524750496487, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 205, "content": "            \"LOCAL_FINGERPRINT\"", "pattern_type": "Local fingerprint generation (monitor for transmission) (LOCAL_FINGERPRINT)", "severity": "CRITICAL", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 687326500}, "context_lines": ["            \"Local fingerprint generation (monitor for transmission)\",", "            \"CRITICAL\",", "            \"LOCAL_FINGERPRINT\"", "        );", "", "        Self::add_pattern(patterns, \"fingerprint_transmission\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.243888245591589, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.243888245591589, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 13, "content": "pub struct TelemetryPatterns {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 695454200}, "context_lines": ["", "#[derive(Debug)]", "pub struct TelemetryPatterns {", "    pub patterns: HashMap<String, PatternInfo>,", "}", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.802910079649727, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.802910079649727, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 17, "content": "impl TelemetryPatterns {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 703039200}, "context_lines": ["}", "", "impl TelemetryPatterns {", "    pub fn new() -> Self {", "        Self::new_advanced(\"medium\")", "    }"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.7201755214643453, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.7201755214643453, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 44, "content": "        TelemetryPatterns { patterns }", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 708475500}, "context_lines": ["        }", "", "        TelemetryPatterns { patterns }", "    }", "", "    fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.352456033021286, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.352456033021286, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 85, "content": "        // Basic Telemetry patterns", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 713690900}, "context_lines": ["        );", "", "        // Basic Telemetry patterns", "        Self::add_pattern(patterns, \"telemetry_collection\",", "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "            \"Telemetry/Analytics collection\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.449036293882859, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.449036293882859, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 86, "content": "        Self::add_pattern(patterns, \"telemetry_collection\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 718832600}, "context_lines": ["", "        // Basic Telemetry patterns", "        Self::add_pattern(patterns, \"telemetry_collection\",", "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "            \"Telemetry/Analytics collection\",", "            \"HIGH\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.084389550577473, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.084389550577473, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 87, "content": "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 724212400}, "context_lines": ["        // Basic Telemetry patterns", "        Self::add_pattern(patterns, \"telemetry_collection\",", "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "            \"Telemetry/Analytics collection\",", "            \"HIGH\",", "            \"TELEMETRY\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.310940784961229, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.310940784961229, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 88, "content": "            \"Telemetry/Analytics collection\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 729740300}, "context_lines": ["        Self::add_pattern(patterns, \"telemetry_collection\",", "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "            \"Telemetry/Analytics collection\",", "            \"HIGH\",", "            \"TELEMETRY\"", "        );"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.6337310665483167, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.6337310665483167, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 90, "content": "            \"TELEMETRY\"", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 737237000}, "context_lines": ["            \"Telemetry/Analytics collection\",", "            \"HIGH\",", "            \"TELEMETRY\"", "        );", "", "        Self::add_pattern(patterns, \"data_transmission\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.2724994555866935, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.2724994555866935, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 97, "content": "            \"TELEMETRY\"", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 742294000}, "context_lines": ["            \"Data transmission to external servers\",", "            \"HIGH\",", "            \"TELEMETRY\"", "        );", "    }", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.2724994555866935, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.2724994555866935, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 104, "content": "            r\"(?i)(user[_-]?behavior|click[_-]?tracking|session[_-]?data|activity[_-]?log)\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 747231700}, "context_lines": ["        // Medium penetration patterns", "        Self::add_pattern(patterns, \"user_behavior\",", "            r\"(?i)(user[_-]?behavior|click[_-]?tracking|session[_-]?data|activity[_-]?log)\",", "            \"User behavior tracking\",", "            \"MEDIUM\",", "            \"TELEMETRY\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.565355674335043, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.565355674335043, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 105, "content": "            \"User behavior tracking\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 752288500}, "context_lines": ["        Self::add_pattern(patterns, \"user_behavior\",", "            r\"(?i)(user[_-]?behavior|click[_-]?tracking|session[_-]?data|activity[_-]?log)\",", "            \"User behavior tracking\",", "            \"MEDIUM\",", "            \"TELEMETRY\"", "        );"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.4241058950621954, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.4241058950621954, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 107, "content": "            \"TELEMETRY\"", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 756924000}, "context_lines": ["            \"User behavior tracking\",", "            \"MEDIUM\",", "            \"TELEMETRY\"", "        );", "", "        Self::add_pattern(patterns, \"crash_reporting\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.2724994555866935, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.2724994555866935, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 111, "content": "            r\"(?i)(crash[_-]?report|error[_-]?report|exception[_-]?tracking)\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 761903300}, "context_lines": ["", "        Self::add_pattern(patterns, \"crash_reporting\",", "            r\"(?i)(crash[_-]?report|error[_-]?report|exception[_-]?tracking)\",", "            \"Crash/Error reporting\",", "            \"MEDIUM\",", "            \"TELEMETRY\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.2708806297967525, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.2708806297967525, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 114, "content": "            \"TELEMETRY\"", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 768428400}, "context_lines": ["            \"Crash/Error reporting\",", "            \"MEDIUM\",", "            \"TELEMETRY\"", "        );", "", "        // System information patterns"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.272499455586694, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.272499455586694, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 150, "content": "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 774106900}, "context_lines": ["", "        Self::add_pattern(patterns, \"complex_structures\",", "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "            \"Complex data structures for tracking\",", "            \"HIGH\",", "            \"STRUCTURE\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.473231803727736, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.473231803727736, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 151, "content": "            \"Complex data structures for tracking\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 779700100}, "context_lines": ["        Self::add_pattern(patterns, \"complex_structures\",", "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "            \"Complex data structures for tracking\",", "            \"HIGH\",", "            \"STRUCTURE\"", "        );"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.77527068506633, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.77527068506633, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 179, "content": "            r\"(?i)(\\.com|\\.net|\\.org|\\.io|analytics|telemetry|tracking)\",", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 784816600}, "context_lines": ["", "        Self::add_pattern(patterns, \"external_domains\",", "            r\"(?i)(\\.com|\\.net|\\.org|\\.io|analytics|telemetry|tracking)\",", "            \"External domain references\",", "            \"LOW\",", "            \"NETWORK\""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.228749004139311, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.228749004139311, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 50, "content": "        Self::add_pattern(patterns, \"machine_id_generation\",", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 790622600}, "context_lines": ["", "        // Basic Machine ID patterns", "        Self::add_pattern(patterns, \"machine_id_generation\",", "            r\"(?i)(machine[_-]?id|device[_-]?id|hardware[_-]?id|system[_-]?id|unique[_-]?id)\",", "            \"Machine/Device ID generation or usage\",", "            \"HIGH\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.1420977381206505, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.1420977381206505, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 54, "content": "            \"MACHINE_ID\"", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 796547300}, "context_lines": ["            \"Machine/Device ID generation or usage\",", "            \"HIGH\",", "            \"MACHINE_ID\"", "        );", "", "        Self::add_pattern(patterns, \"uuid_generation\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.6258145836939115, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.6258145836939115, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 61, "content": "            \"MACHINE_ID\"", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 802696400}, "context_lines": ["            \"UUID generation for identification\",", "            \"MEDIUM\",", "            \"MACHINE_ID\"", "        );", "", "        Self::add_pattern(patterns, \"mac_address\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.6258145836939115, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.6258145836939115, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 68, "content": "            \"MACHINE_ID\"", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 807730000}, "context_lines": ["            \"MAC address collection\",", "            \"HIGH\",", "            \"MACHINE_ID\"", "        );", "", "        Self::add_pattern(patterns, \"cpu_info\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.6258145836939115, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.6258145836939115, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 75, "content": "            \"MACHINE_ID\"", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 812888500}, "context_lines": ["            \"CPU information collection\",", "            \"HIGH\",", "            \"MACHINE_ID\"", "        );", "", "        Self::add_pattern(patterns, \"disk_serial\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.6258145836939115, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.6258145836939115, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 82, "content": "            \"MACHINE_ID\"", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 817945600}, "context_lines": ["            \"Disk/Drive serial number collection\",", "            \"HIGH\",", "            \"MACHINE_ID\"", "        );", "", "        // Basic Telemetry patterns"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.6258145836939115, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.6258145836939115, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 231, "content": "            r\"(?i)(machine_uid|get_machine_id)\",", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 823827100}, "context_lines": ["", "        Self::add_pattern(patterns, \"rust_machine_uid\",", "            r\"(?i)(machine_uid|get_machine_id)\",", "            \"Rust machine-uid crate usage\",", "            \"HIGH\",", "            \"RUST_SPECIFIC\""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.8737340530666224, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.8737340530666224, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 71, "content": "        Self::add_pattern(patterns, \"cpu_info\",", "pattern_type": "CPU information collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 829357400}, "context_lines": ["        );", "", "        Self::add_pattern(patterns, \"cpu_info\",", "            r\"(?i)(cpu[_-]?info|processor[_-]?id|cpu[_-]?serial|cpuid)\",", "            \"CPU information collection\",", "            \"HIGH\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "cpu_info", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.074825553344984, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.074825553344984, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 72, "content": "            r\"(?i)(cpu[_-]?info|processor[_-]?id|cpu[_-]?serial|cpuid)\",", "pattern_type": "CPU information collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 834520100}, "context_lines": ["", "        Self::add_pattern(patterns, \"cpu_info\",", "            r\"(?i)(cpu[_-]?info|processor[_-]?id|cpu[_-]?serial|cpuid)\",", "            \"CPU information collection\",", "            \"HIGH\",", "            \"MACHINE_ID\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "cpu_info", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.299532077825667, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.299532077825667, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 103, "content": "        Self::add_pattern(patterns, \"user_behavior\",", "pattern_type": "User behavior tracking (TELEMETRY)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 840186200}, "context_lines": ["    fn add_medium_patterns(patterns: &mut HashMap<String, PatternInfo>) {", "        // Medium penetration patterns", "        Self::add_pattern(patterns, \"user_behavior\",", "            r\"(?i)(user[_-]?behavior|click[_-]?tracking|session[_-]?data|activity[_-]?log)\",", "            \"User behavior tracking\",", "            \"MEDIUM\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.159305766459983, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.159305766459983, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 164, "content": "            r\"(?i)(pipeline|stream|flow|chain|sequence).*[_-]?(data|info|track)\",", "pattern_type": "Data flow analysis patterns (DATA_FLOW)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 845383700}, "context_lines": ["", "        Self::add_pattern(patterns, \"data_flow_patterns\",", "            r\"(?i)(pipeline|stream|flow|chain|sequence).*[_-]?(data|info|track)\",", "            \"Data flow analysis patterns\",", "            \"MEDIUM\",", "            \"DATA_FLOW\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.574012737736493, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.574012737736493, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 110, "content": "        Self::add_pattern(patterns, \"crash_reporting\",", "pattern_type": "Crash/Error reporting (TELEMETRY)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 850729500}, "context_lines": ["        );", "", "        Self::add_pattern(patterns, \"crash_reporting\",", "            r\"(?i)(crash[_-]?report|error[_-]?report|exception[_-]?tracking)\",", "            \"Crash/Error reporting\",", "            \"MEDIUM\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.101954521308259, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.101954521308259, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 143, "content": "            r\"(?i)(base64|hex|encode|decode|encrypt|decrypt|hash|sha|md5).*[_-]?(id|uid|key)\",", "pattern_type": "Hash function usage (good for privacy) (PRIVACY_PROTECTION)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 856602000}, "context_lines": ["        // High penetration patterns - obfuscated and complex structures", "        Self::add_pattern(patterns, \"obfuscated_ids\",", "            r\"(?i)(base64|hex|encode|decode|encrypt|decrypt|hash|sha|md5).*[_-]?(id|uid|key)\",", "            \"Obfuscated ID generation/processing\",", "            \"HIGH\",", "            \"OBFUSCATED\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.579015948060022, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.579015948060022, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 216, "content": "            r\"(?i)(sha256|sha1|md5|blake2|hash[_-]?digest)\",", "pattern_type": "Hash function usage (good for privacy) (PRIVACY_PROTECTION)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 861699100}, "context_lines": ["", "        Self::add_pattern(patterns, \"hash_functions\",", "            r\"(?i)(sha256|sha1|md5|blake2|hash[_-]?digest)\",", "            \"Hash function usage (good for privacy)\",", "            \"LOW\",", "            \"PRIVACY_PROTECTION\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.356564762130954, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.356564762130954, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 78, "content": "        Self::add_pattern(patterns, \"disk_serial\",", "pattern_type": "Disk/Drive serial number collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 867326400}, "context_lines": ["        );", "", "        Self::add_pattern(patterns, \"disk_serial\",", "            r\"(?i)(disk[_-]?serial|drive[_-]?id|volume[_-]?serial)\",", "            \"Disk/Drive serial number collection\",", "            \"HIGH\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "disk_serial", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.9879764393853, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.9879764393853, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 230, "content": "        Self::add_pattern(patterns, \"rust_machine_uid\",", "pattern_type": "Rust machine-uid crate usage (RUST_SPECIFIC)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 873178400}, "context_lines": ["        );", "", "        Self::add_pattern(patterns, \"rust_machine_uid\",", "            r\"(?i)(machine_uid|get_machine_id)\",", "            \"Rust machine-uid crate usage\",", "            \"HIGH\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.160295795414268, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.160295795414268, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 231, "content": "            r\"(?i)(machine_uid|get_machine_id)\",", "pattern_type": "Rust machine-uid crate usage (RUST_SPECIFIC)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 879009400}, "context_lines": ["", "        Self::add_pattern(patterns, \"rust_machine_uid\",", "            r\"(?i)(machine_uid|get_machine_id)\",", "            \"Rust machine-uid crate usage\",", "            \"HIGH\",", "            \"RUST_SPECIFIC\""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.873734053066623, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.873734053066623, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 238, "content": "            r\"(?i)(reqwest::|Client::new|post|get.*http)\",", "pattern_type": "Rust reqwest HTTP client usage (RUST_SPECIFIC)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 885733000}, "context_lines": ["", "        Self::add_pattern(patterns, \"rust_reqwest\",", "            r\"(?i)(reqwest::|Client::new|post|get.*http)\",", "            \"Rust reqwest HTTP client usage\",", "            \"MEDIUM\",", "            \"RUST_SPECIFIC\""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.118431805652036, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.118431805652036, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 132, "content": "        Self::add_pattern(patterns, \"wmi_queries\",", "pattern_type": "WMI queries for system information (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 893197600}, "context_lines": ["        );", "", "        Self::add_pattern(patterns, \"wmi_queries\",", "            r\"(?i)(wmi|win32_|cim_|select.*from.*win32)\",", "            \"WMI queries for system information\",", "            \"HIGH\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "system_information", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.130881379939832, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.130881379939832, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 133, "content": "            r\"(?i)(wmi|win32_|cim_|select.*from.*win32)\",", "pattern_type": "WMI queries for system information (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 900010000}, "context_lines": ["", "        Self::add_pattern(patterns, \"wmi_queries\",", "            r\"(?i)(wmi|win32_|cim_|select.*from.*win32)\",", "            \"WMI queries for system information\",", "            \"HIGH\",", "            \"SYSTEM_INFO\""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "system_information", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.203173295049284, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.203173295049284, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 134, "content": "            \"WMI queries for system information\",", "pattern_type": "WMI queries for system information (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 906119000}, "context_lines": ["        Self::add_pattern(patterns, \"wmi_queries\",", "            r\"(?i)(wmi|win32_|cim_|select.*from.*win32)\",", "            \"WMI queries for system information\",", "            \"HIGH\",", "            \"SYSTEM_INFO\"", "        );"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "system_information", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.619313160220977, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.619313160220977, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 125, "content": "        Self::add_pattern(patterns, \"registry_access\",", "pattern_type": "Windows Registry access (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 912260600}, "context_lines": ["        );", "", "        Self::add_pattern(patterns, \"registry_access\",", "            r\"(?i)(registry|hkey_|regkey|winreg)\",", "            \"Windows Registry access\",", "            \"HIGH\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "system_information", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.05583924361061, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.05583924361061, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 126, "content": "            r\"(?i)(registry|hkey_|regkey|winreg)\",", "pattern_type": "Windows Registry access (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 917672800}, "context_lines": ["", "        Self::add_pattern(patterns, \"registry_access\",", "            r\"(?i)(registry|hkey_|regkey|winreg)\",", "            \"Windows Registry access\",", "            \"HIGH\",", "            \"SYSTEM_INFO\""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "system_information", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.778688570451096, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.778688570451096, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 127, "content": "            \"Windows Registry access\",", "pattern_type": "Windows Registry access (SYSTEM_INFO)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 922771300}, "context_lines": ["        Self::add_pattern(patterns, \"registry_access\",", "            r\"(?i)(registry|hkey_|regkey|winreg)\",", "            \"Windows Registry access\",", "            \"HIGH\",", "            \"SYSTEM_INFO\"", "        );"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "system_information", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.4241651737381518, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.4241651737381518, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 156, "content": "        Self::add_pattern(patterns, \"parent_child_refs\",", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 927366500}, "context_lines": ["        );", "", "        Self::add_pattern(patterns, \"parent_child_refs\",", "            r\"(?i)(parent|child|hierarchy|tree|node).*[_-]?(id|ref|link)\",", "            \"Parent-child relationship patterns\",", "            \"MEDIUM\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.043637851155671, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.043637851155671, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 157, "content": "            r\"(?i)(parent|child|hierarchy|tree|node).*[_-]?(id|ref|link)\",", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 932025000}, "context_lines": ["", "        Self::add_pattern(patterns, \"parent_child_refs\",", "            r\"(?i)(parent|child|hierarchy|tree|node).*[_-]?(id|ref|link)\",", "            \"Parent-child relationship patterns\",", "            \"MEDIUM\",", "            \"STRUCTURE\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.359008054725046, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.359008054725046, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 118, "content": "        Self::add_pattern(patterns, \"system_info\",", "pattern_type": "System information collection (SYSTEM_INFO)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 937174700}, "context_lines": ["", "        // System information patterns", "        Self::add_pattern(patterns, \"system_info\",", "            r\"(?i)(system[_-]?info|os[_-]?version|platform[_-]?info|environment[_-]?info)\",", "            \"System information collection\",", "            \"MEDIUM\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "system_information", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.075783629896563, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.075783629896563, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 122, "content": "            \"SYSTEM_INFO\"", "pattern_type": "System information collection (SYSTEM_INFO)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 941451700}, "context_lines": ["            \"System information collection\",", "            \"MEDIUM\",", "            \"SYSTEM_INFO\"", "        );", "", "        Self::add_pattern(patterns, \"registry_access\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "system_information", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.7630741894285693, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.7630741894285693, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 129, "content": "            \"SYSTEM_INFO\"", "pattern_type": "System information collection (SYSTEM_INFO)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 946089500}, "context_lines": ["            \"Windows Registry access\",", "            \"HIGH\",", "            \"SYSTEM_INFO\"", "        );", "", "        Self::add_pattern(patterns, \"wmi_queries\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "system_information", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.7630741894285693, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.7630741894285693, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 136, "content": "            \"SYSTEM_INFO\"", "pattern_type": "System information collection (SYSTEM_INFO)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 950557800}, "context_lines": ["            \"WMI queries for system information\",", "            \"HIGH\",", "            \"SYSTEM_INFO\"", "        );", "    }", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "system_information", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.7630741894285693, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.7630741894285693, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 94, "content": "            r\"(?i)(send[_-]?data|transmit|upload[_-]?stats|report[_-]?usage)\",", "pattern_type": "Data transmission to external servers (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 955375000}, "context_lines": ["", "        Self::add_pattern(patterns, \"data_transmission\",", "            r\"(?i)(send[_-]?data|transmit|upload[_-]?stats|report[_-]?usage)\",", "            \"Data transmission to external servers\",", "            \"HIGH\",", "            \"TELEMETRY\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.35036435979957, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.35036435979957, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 209, "content": "            r\"(?i)(send[_-]?fingerprint|transmit[_-]?fingerprint|upload[_-]?fingerprint)\",", "pattern_type": "Data transmission to external servers (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 959885500}, "context_lines": ["", "        Self::add_pattern(patterns, \"fingerprint_transmission\",", "            r\"(?i)(send[_-]?fingerprint|transmit[_-]?fingerprint|upload[_-]?fingerprint)\",", "            \"Fingerprint transmission to external servers\",", "            \"CRITICAL\",", "            \"FINGERPRINT_TRANSMISSION\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.3456473685955475, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.3456473685955475, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 13, "content": "pub struct TelemetryPatterns {", "pattern_type": "Complex data structures for tracking (STRUCTURE)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 965108400}, "context_lines": ["", "#[derive(Debug)]", "pub struct TelemetryPatterns {", "    pub patterns: HashMap<String, PatternInfo>,", "}", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.802910079649727, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.802910079649727, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 150, "content": "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "pattern_type": "Complex data structures for tracking (STRUCTURE)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 970460500}, "context_lines": ["", "        Self::add_pattern(patterns, \"complex_structures\",", "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "            \"Complex data structures for tracking\",", "            \"HIGH\",", "            \"STRUCTURE\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.473231803727736, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.473231803727736, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 151, "content": "            \"Complex data structures for tracking\",", "pattern_type": "Complex data structures for tracking (STRUCTURE)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 975112400}, "context_lines": ["        Self::add_pattern(patterns, \"complex_structures\",", "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "            \"Complex data structures for tracking\",", "            \"HIGH\",", "            \"STRUCTURE\"", "        );"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.7752706850663302, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.7752706850663302, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 64, "content": "        Self::add_pattern(patterns, \"mac_address\",", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 979850000}, "context_lines": ["        );", "", "        Self::add_pattern(patterns, \"mac_address\",", "            r\"(?i)(mac[_-]?address|get[_-]?mac|network[_-]?interface)\",", "            \"MAC address collection\",", "            \"HIGH\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.9308813799398323, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.9308813799398323, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 231, "content": "            r\"(?i)(machine_uid|get_machine_id)\",", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 984643500}, "context_lines": ["", "        Self::add_pattern(patterns, \"rust_machine_uid\",", "            r\"(?i)(machine_uid|get_machine_id)\",", "            \"Rust machine-uid crate usage\",", "            \"HIGH\",", "            \"RUST_SPECIFIC\""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.8737340530666224, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.8737340530666224, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 224, "content": "            r\"(?i)(sysinfo::|System::new|get_processor|get_networks)\",", "pattern_type": "Rust sysinfo crate usage (RUST_SPECIFIC)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 989478500}, "context_lines": ["        // Rust-specific patterns", "        Self::add_pattern(patterns, \"rust_sysinfo\",", "            r\"(?i)(sysinfo::|System::new|get_processor|get_networks)\",", "            \"Rust sysinfo crate usage\",", "            \"HIGH\",", "            \"RUST_SPECIFIC\""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.250013373778756, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.250013373778756, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 58, "content": "            r\"(?i)(uuid::v[1-5]|Uuid::new|uuid\\.new|generate[_-]?uuid)\",", "pattern_type": "UUID generation for identification (MACHINE_ID)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 994141100}, "context_lines": ["", "        Self::add_pattern(patterns, \"uuid_generation\",", "            r\"(?i)(uuid::v[1-5]|Uuid::new|uuid\\.new|generate[_-]?uuid)\",", "            \"UUID generation for identification\",", "            \"MEDIUM\",", "            \"MACHINE_ID\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "uuid", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.372701523438733, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.372701523438733, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 13, "content": "pub struct TelemetryPatterns {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 998880000}, "context_lines": ["", "#[derive(Debug)]", "pub struct TelemetryPatterns {", "    pub patterns: HashMap<String, PatternInfo>,", "}", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.8029100796497275, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.8029100796497275, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 17, "content": "impl TelemetryPatterns {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 3434900}, "context_lines": ["}", "", "impl TelemetryPatterns {", "    pub fn new() -> Self {", "        Self::new_advanced(\"medium\")", "    }"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.7201755214643453, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.7201755214643453, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 44, "content": "        TelemetryPatterns { patterns }", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 8121800}, "context_lines": ["        }", "", "        TelemetryPatterns { patterns }", "    }", "", "    fn add_base_patterns(patterns: &mut HashMap<String, PatternInfo>) {"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.3524560330212863, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.3524560330212863, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 85, "content": "        // Basic Telemetry patterns", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 13055400}, "context_lines": ["        );", "", "        // Basic Telemetry patterns", "        Self::add_pattern(patterns, \"telemetry_collection\",", "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "            \"Telemetry/Analytics collection\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.449036293882859, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.449036293882859, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 86, "content": "        Self::add_pattern(patterns, \"telemetry_collection\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 17638100}, "context_lines": ["", "        // Basic Telemetry patterns", "        Self::add_pattern(patterns, \"telemetry_collection\",", "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "            \"Telemetry/Analytics collection\",", "            \"HIGH\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.084389550577473, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.084389550577473, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 87, "content": "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 22691700}, "context_lines": ["        // Basic Telemetry patterns", "        Self::add_pattern(patterns, \"telemetry_collection\",", "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "            \"Telemetry/Analytics collection\",", "            \"HIGH\",", "            \"TELEMETRY\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.310940784961229, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.310940784961229, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 88, "content": "            \"Telemetry/Analytics collection\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 27557400}, "context_lines": ["        Self::add_pattern(patterns, \"telemetry_collection\",", "            r\"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)\",", "            \"Telemetry/Analytics collection\",", "            \"HIGH\",", "            \"TELEMETRY\"", "        );"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.633731066548317, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.633731066548317, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 90, "content": "            \"TELEMETRY\"", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 31979300}, "context_lines": ["            \"Telemetry/Analytics collection\",", "            \"HIGH\",", "            \"TELEMETRY\"", "        );", "", "        Self::add_pattern(patterns, \"data_transmission\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.272499455586694, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.272499455586694, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 97, "content": "            \"TELEMETRY\"", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 36196000}, "context_lines": ["            \"Data transmission to external servers\",", "            \"HIGH\",", "            \"TELEMETRY\"", "        );", "    }", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.272499455586694, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.272499455586694, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 104, "content": "            r\"(?i)(user[_-]?behavior|click[_-]?tracking|session[_-]?data|activity[_-]?log)\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 40856800}, "context_lines": ["        // Medium penetration patterns", "        Self::add_pattern(patterns, \"user_behavior\",", "            r\"(?i)(user[_-]?behavior|click[_-]?tracking|session[_-]?data|activity[_-]?log)\",", "            \"User behavior tracking\",", "            \"MEDIUM\",", "            \"TELEMETRY\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.565355674335043, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.565355674335043, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 105, "content": "            \"User behavior tracking\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 45428600}, "context_lines": ["        Self::add_pattern(patterns, \"user_behavior\",", "            r\"(?i)(user[_-]?behavior|click[_-]?tracking|session[_-]?data|activity[_-]?log)\",", "            \"User behavior tracking\",", "            \"MEDIUM\",", "            \"TELEMETRY\"", "        );"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.4241058950621954, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.4241058950621954, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 107, "content": "            \"TELEMETRY\"", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 49734700}, "context_lines": ["            \"User behavior tracking\",", "            \"MEDIUM\",", "            \"TELEMETRY\"", "        );", "", "        Self::add_pattern(patterns, \"crash_reporting\","], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.2724994555866935, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.2724994555866935, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 111, "content": "            r\"(?i)(crash[_-]?report|error[_-]?report|exception[_-]?tracking)\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 54633800}, "context_lines": ["", "        Self::add_pattern(patterns, \"crash_reporting\",", "            r\"(?i)(crash[_-]?report|error[_-]?report|exception[_-]?tracking)\",", "            \"Crash/Error reporting\",", "            \"MEDIUM\",", "            \"TELEMETRY\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.2708806297967525, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.2708806297967525, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 114, "content": "            \"TELEMETRY\"", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 59196100}, "context_lines": ["            \"Crash/Error reporting\",", "            \"MEDIUM\",", "            \"TELEMETRY\"", "        );", "", "        // System information patterns"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.2724994555866935, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.2724994555866935, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 150, "content": "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 63710500}, "context_lines": ["", "        Self::add_pattern(patterns, \"complex_structures\",", "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "            \"Complex data structures for tracking\",", "            \"HIGH\",", "            \"STRUCTURE\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.473231803727736, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.473231803727736, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 151, "content": "            \"Complex data structures for tracking\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 68206400}, "context_lines": ["        Self::add_pattern(patterns, \"complex_structures\",", "            r\"(?i)(struct|class|interface).*[_-]?(telemetry|tracking|analytics|machine|device)\",", "            \"Complex data structures for tracking\",", "            \"HIGH\",", "            \"STRUCTURE\"", "        );"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.775270685066331, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.775270685066331, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 179, "content": "            r\"(?i)(\\.com|\\.net|\\.org|\\.io|analytics|telemetry|tracking)\",", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 73592400}, "context_lines": ["", "        Self::add_pattern(patterns, \"external_domains\",", "            r\"(?i)(\\.com|\\.net|\\.org|\\.io|analytics|telemetry|tracking)\",", "            \"External domain references\",", "            \"LOW\",", "            \"NETWORK\""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.228749004139311, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.228749004139311, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\patterns.rs", "line_number": 143, "content": "            r\"(?i)(base64|hex|encode|decode|encrypt|decrypt|hash|sha|md5).*[_-]?(id|uid|key)\",", "pattern_type": "Obfuscated ID generation/processing (OBFUSCATED)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 78616800}, "context_lines": ["        // High penetration patterns - obfuscated and complex structures", "        Self::add_pattern(patterns, \"obfuscated_ids\",", "            r\"(?i)(base64|hex|encode|decode|encrypt|decrypt|hash|sha|md5).*[_-]?(id|uid|key)\",", "            \"Obfuscated ID generation/processing\",", "            \"HIGH\",", "            \"OBFUSCATED\""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.579015948060021, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.579015948060021, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 17, "content": "    machine_fingerprint: MachineFingerprint,", "pattern_type": "Local fingerprint generation (monitor for transmission) (LOCAL_FINGERPRINT)", "severity": "CRITICAL", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 108420100}, "context_lines": ["    penetration_level: String,", "    privacy_analysis: bool,", "    machine_fingerprint: MachineFingerprint,", "}", "", "impl FileScanner {"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.8542227440216004, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.8542227440216004, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 29, "content": "            machine_fingerprint: MachineFingerprint::generate(),", "pattern_type": "Local fingerprint generation (monitor for transmission) (LOCAL_FINGERPRINT)", "severity": "CRITICAL", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 113335800}, "context_lines": ["            penetration_level: \"medium\".to_string(),", "            privacy_analysis: false,", "            machine_fingerprint: MachineFingerprint::generate(),", "        }", "    }", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.8195679573115897, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.8195679573115897, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 45, "content": "            machine_fingerprint: MachineFingerprint::generate(),", "pattern_type": "Local fingerprint generation (monitor for transmission) (LOCAL_FINGERPRINT)", "severity": "CRITICAL", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 118142100}, "context_lines": ["            penetration_level: \"high\".to_string(),", "            privacy_analysis,", "            machine_fingerprint: MachineFingerprint::generate(),", "        }", "    }", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.81956795731159, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.81956795731159, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 117, "content": "            machine_fingerprint: self.machine_fingerprint.fingerprint().to_string(),", "pattern_type": "Local fingerprint generation (monitor for transmission) (LOCAL_FINGERPRINT)", "severity": "CRITICAL", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 123334400}, "context_lines": ["            obfuscation_patterns: Vec::new(),", "            scan_id: uuid::Uuid::new_v4().to_string(),", "            machine_fingerprint: self.machine_fingerprint.fingerprint().to_string(),", "            fingerprint_entropy: self.machine_fingerprint.entropy_score(),", "            privacy_summary,", "        })"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.056603984666222, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.056603984666222, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 118, "content": "            fingerprint_entropy: self.machine_fingerprint.entropy_score(),", "pattern_type": "Local fingerprint generation (monitor for transmission) (LOCAL_FINGERPRINT)", "severity": "CRITICAL", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 127955000}, "context_lines": ["            scan_id: uuid::Uuid::new_v4().to_string(),", "            machine_fingerprint: self.machine_fingerprint.fingerprint().to_string(),", "            fingerprint_entropy: self.machine_fingerprint.entropy_score(),", "            privacy_summary,", "        })", "    }"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.061690221305015, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.061690221305015, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 1, "content": "use crate::{Detection, ScanResult, TelemetryPatterns, PrivacySummary};", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 132514400}, "context_lines": ["use crate::{Detection, ScanResult, TelemetryPatterns, PrivacySummary};", "use crate::fingerprint::{MachineFingerprint, analyze_id_string, PrivacyRisk};", "use std::collections::HashMap;", "use std::fs;"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.330291367955844, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.330291367955844, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 11, "content": "    patterns: TelemetryPatterns,", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 138647500}, "context_lines": ["", "pub struct FileScanner {", "    patterns: TelemetryPatterns,", "    extensions: Vec<String>,", "    deep_scan: bool,", "    analyze_structures: bool,"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.5755059710789405, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.5755059710789405, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 21, "content": "    pub fn new(patterns: TelemetryPatterns, extensions: Vec<&str>) -> Self {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 143619400}, "context_lines": ["", "impl FileScanner {", "    pub fn new(patterns: TelemetryPatterns, extensions: Vec<&str>) -> Self {", "        Self {", "            patterns,", "            extensions: extensions.iter().map(|s| s.to_string()).collect(),"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.366220528039711, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.366220528039711, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 33, "content": "    pub fn new_advanced(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool) -> Self {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 148064000}, "context_lines": ["    }", "", "    pub fn new_advanced(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool) -> Self {", "        Self::new_with_privacy(patterns, extensions, deep_scan, analyze_structures, false)", "    }", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.558032231400985, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.558032231400985, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 37, "content": "    pub fn new_with_privacy(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool, privacy_analysis: bool) -> Self {", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 152825000}, "context_lines": ["    }", "", "    pub fn new_with_privacy(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool, privacy_analysis: bool) -> Self {", "        Self {", "            patterns,", "            extensions: extensions.iter().map(|s| s.to_string()).collect(),"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.634283436066943, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.634283436066943, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 304, "content": "            \"TELEMETRY\" => Some(\"telemetry_data\".to_string()),", "pattern_type": "Telemetry/Analytics collection (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 157794600}, "context_lines": ["                }", "            }", "            \"TELEMETRY\" => Some(\"telemetry_data\".to_string()),", "            \"SYSTEM_INFO\" => Some(\"system_information\".to_string()),", "            \"NETWORK\" => Some(\"network_data\".to_string()),", "            _ => None,"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.314397973525477, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.314397973525477, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 291, "content": "            \"MACHINE_ID\" => {", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 162803200}, "context_lines": ["        // Detect what type of data is being collected based on patterns", "        match category {", "            \"MACHINE_ID\" => {", "                if line_content.to_lowercase().contains(\"mac\") {", "                    Some(\"mac_address\".to_string())", "                } else if line_content.to_lowercase().contains(\"cpu\") {"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 2.8820165499963153, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 2.8820165499963153, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 301, "content": "                    Some(\"machine_identifier\".to_string())", "pattern_type": "Machine/Device ID generation or usage (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 168916000}, "context_lines": ["                    Some(\"uuid\".to_string())", "                } else {", "                    Some(\"machine_identifier\".to_string())", "                }", "            }", "            \"TELEMETRY\" => Some(\"telemetry_data\".to_string()),"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.624222764498073, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.624222764498073, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 295, "content": "                    Some(\"cpu_info\".to_string())", "pattern_type": "CPU information collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 173765200}, "context_lines": ["                    Some(\"mac_address\".to_string())", "                } else if line_content.to_lowercase().contains(\"cpu\") {", "                    Some(\"cpu_info\".to_string())", "                } else if line_content.to_lowercase().contains(\"disk\") {", "                    Some(\"disk_serial\".to_string())", "                } else if line_content.to_lowercase().contains(\"uuid\") {"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "cpu_info", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.393432304889683, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.393432304889683, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 167, "content": "                    data_flow_analysis: self.analyze_data_flow(&content, line_number),", "pattern_type": "Data flow analysis patterns (DATA_FLOW)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 180542700}, "context_lines": ["                    child_references: self.find_child_references(&content, &line_content),", "                    obfuscation_level: self.detect_obfuscation(&line_content),", "                    data_flow_analysis: self.analyze_data_flow(&content, line_number),", "                    data_collected,", "                    is_hashed,", "                    privacy_risk,"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.000555579422581, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.000555579422581, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 297, "content": "                    Some(\"disk_serial\".to_string())", "pattern_type": "Disk/Drive serial number collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 186212800}, "context_lines": ["                    Some(\"cpu_info\".to_string())", "                } else if line_content.to_lowercase().contains(\"disk\") {", "                    Some(\"disk_serial\".to_string())", "                } else if line_content.to_lowercase().contains(\"uuid\") {", "                    Some(\"uuid\".to_string())", "                } else {"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "disk_serial", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.477359912519062, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.477359912519062, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 342, "content": "               detection.content.to_lowercase().contains(\"post\") {", "pattern_type": "Rust reqwest HTTP client usage (RUST_SPECIFIC)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 193336500}, "context_lines": ["            if detection.content.to_lowercase().contains(\"transmit\") ||", "               detection.content.to_lowercase().contains(\"send\") ||", "               detection.content.to_lowercase().contains(\"post\") {", "                if detection.data_collected.is_some() {", "                    summary.fingerprint_transmissions += 1;", "                }"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.7733287343292004, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.7733287343292004, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 165, "content": "                    child_references: self.find_child_references(&content, &line_content),", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 198611300}, "context_lines": ["                    context_lines: self.get_context_lines(&content, line_number, 3),", "                    parent_structure: self.analyze_parent_structure(&content, line_number),", "                    child_references: self.find_child_references(&content, &line_content),", "                    obfuscation_level: self.detect_obfuscation(&line_content),", "                    data_flow_analysis: self.analyze_data_flow(&content, line_number),", "                    data_collected,"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.7568909226926936, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.7568909226926936, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 214, "content": "    fn find_child_references(&self, _content: &str, current_line: &str) -> Vec<String> {", "pattern_type": "Parent-child relationship patterns (STRUCTURE)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 203383900}, "context_lines": ["    }", "", "    fn find_child_references(&self, _content: &str, current_line: &str) -> Vec<String> {", "        if !self.analyze_structures {", "            return Vec::new();", "        }"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.279550369101745, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.279550369101745, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 305, "content": "            \"SYSTEM_INFO\" => Some(\"system_information\".to_string()),", "pattern_type": "System information collection (SYSTEM_INFO)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 208159000}, "context_lines": ["            }", "            \"TELEMETRY\" => Some(\"telemetry_data\".to_string()),", "            \"SYSTEM_INFO\" => Some(\"system_information\".to_string()),", "            \"NETWORK\" => Some(\"network_data\".to_string()),", "            _ => None,", "        }"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "system_information", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.4134585188711135, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.4134585188711135, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 273, "content": "        if current_line.contains(\"=\") && (current_line.contains(\"send\") || current_line.contains(\"transmit\")) {", "pattern_type": "Data transmission to external servers (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 215287800}, "context_lines": ["", "        // Simple data flow analysis - look for assignments and function calls", "        if current_line.contains(\"=\") && (current_line.contains(\"send\") || current_line.contains(\"transmit\")) {", "            return Some(\"DATA_TRANSMISSION\".to_string());", "        }", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "LOW", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.197005643644455, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.197005643644455, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 340, "content": "            if detection.content.to_lowercase().contains(\"transmit\") ||", "pattern_type": "Data transmission to external servers (TELEMETRY)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 220497300}, "context_lines": ["", "            // Check for fingerprint transmission patterns", "            if detection.content.to_lowercase().contains(\"transmit\") ||", "               detection.content.to_lowercase().contains(\"send\") ||", "               detection.content.to_lowercase().contains(\"post\") {", "                if detection.data_collected.is_some() {"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "telemetry_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.948385325582184, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.948385325582184, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 293, "content": "                    Some(\"mac_address\".to_string())", "pattern_type": "MAC address collection (MACHINE_ID)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 225044100}, "context_lines": ["            \"MACHINE_ID\" => {", "                if line_content.to_lowercase().contains(\"mac\") {", "                    Some(\"mac_address\".to_string())", "                } else if line_content.to_lowercase().contains(\"cpu\") {", "                    Some(\"cpu_info\".to_string())", "                } else if line_content.to_lowercase().contains(\"disk\") {"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "mac_address", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.452945941973246, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.452945941973246, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 116, "content": "            scan_id: uuid::Uuid::new_v4().to_string(),", "pattern_type": "UUID generation for identification (MACHINE_ID)", "severity": "MEDIUM", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 232147200}, "context_lines": ["            data_flow_map: HashMap::new(),", "            obfuscation_patterns: Vec::new(),", "            scan_id: uuid::Uuid::new_v4().to_string(),", "            machine_fingerprint: self.machine_fingerprint.fingerprint().to_string(),", "            fingerprint_entropy: self.machine_fingerprint.entropy_score(),", "            privacy_summary,"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "uuid", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.000537375887083, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.000537375887083, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 1, "content": "use crate::{Detection, ScanResult, TelemetryPatterns, PrivacySummary};", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 237947200}, "context_lines": ["use crate::{Detection, ScanResult, TelemetryPatterns, PrivacySummary};", "use crate::fingerprint::{MachineFingerprint, analyze_id_string, PrivacyRisk};", "use std::collections::HashMap;", "use std::fs;"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.330291367955844, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.330291367955844, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 11, "content": "    patterns: TelemetryPatterns,", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 244482200}, "context_lines": ["", "pub struct FileScanner {", "    patterns: TelemetryPatterns,", "    extensions: Vec<String>,", "    deep_scan: bool,", "    analyze_structures: bool,"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.5755059710789405, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.5755059710789405, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 21, "content": "    pub fn new(patterns: TelemetryPatterns, extensions: Vec<&str>) -> Self {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 249347100}, "context_lines": ["", "impl FileScanner {", "    pub fn new(patterns: TelemetryPatterns, extensions: Vec<&str>) -> Self {", "        Self {", "            patterns,", "            extensions: extensions.iter().map(|s| s.to_string()).collect(),"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.366220528039712, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.366220528039712, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 33, "content": "    pub fn new_advanced(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool) -> Self {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 254160200}, "context_lines": ["    }", "", "    pub fn new_advanced(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool) -> Self {", "        Self::new_with_privacy(patterns, extensions, deep_scan, analyze_structures, false)", "    }", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.558032231400985, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.558032231400985, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 37, "content": "    pub fn new_with_privacy(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool, privacy_analysis: bool) -> Self {", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 258733500}, "context_lines": ["    }", "", "    pub fn new_with_privacy(patterns: TelemetryPatterns, extensions: Vec<&str>, deep_scan: bool, analyze_structures: bool, privacy_analysis: bool) -> Self {", "        Self {", "            patterns,", "            extensions: extensions.iter().map(|s| s.to_string()).collect(),"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.634283436066944, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.634283436066944, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 304, "content": "            \"TELEMETRY\" => Some(\"telemetry_data\".to_string()),", "pattern_type": "External domain references (NETWORK)", "severity": "LOW", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 267286700}, "context_lines": ["                }", "            }", "            \"TELEMETRY\" => Some(\"telemetry_data\".to_string()),", "            \"SYSTEM_INFO\" => Some(\"system_information\".to_string()),", "            \"NETWORK\" => Some(\"network_data\".to_string()),", "            _ => None,"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": "network_data", "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 4.314397973525477, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 4.314397973525477, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 318, "content": "            hashed_ids_detected: 0,", "pattern_type": "Obfuscated ID generation/processing (OBFUSCATED)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 272755800}, "context_lines": ["            low_risks: 0,", "            raw_ids_detected: 0,", "            hashed_ids_detected: 0,", "            fingerprint_transmissions: 0,", "        };", ""], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.069119693063989, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.069119693063989, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 332, "content": "            // Count hashed vs raw IDs", "pattern_type": "Obfuscated ID generation/processing (OBFUSCATED)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 279589200}, "context_lines": ["            }", "", "            // Count hashed vs raw IDs", "            if detection.is_hashed {", "                summary.hashed_ids_detected += 1;", "            } else if detection.data_collected.is_some() {"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.136302360722026, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.136302360722026, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}, {"file_path": "src\\scanner.rs", "line_number": 334, "content": "                summary.hashed_ids_detected += 1;", "pattern_type": "Obfuscated ID generation/processing (OBFUSCATED)", "severity": "HIGH", "timestamp": {"secs_since_epoch": 1751523876, "nanos_since_epoch": 285338400}, "context_lines": ["            // Count hashed vs raw IDs", "            if detection.is_hashed {", "                summary.hashed_ids_detected += 1;", "            } else if detection.data_collected.is_some() {", "                summary.raw_ids_detected += 1;", "            }"], "parent_structure": null, "child_references": [], "obfuscation_level": "NONE", "data_flow_analysis": null, "data_collected": null, "is_hashed": false, "privacy_risk": "MEDIUM", "entropy_score": 3.4552498945616352, "id_analysis": {"is_likely_machine_id": true, "entropy_score": 3.4552498945616352, "format_type": "Mixed", "privacy_risk": "Medium", "is_hashed": false}}], "scan_time": {"secs_since_epoch": 1751523875, "nanos_since_epoch": 233601200}, "files_scanned": 5, "directories_scanned": 1, "penetration_level": "high", "structure_analysis": {}, "data_flow_map": {}, "obfuscation_patterns": [], "scan_id": "247f1f9b-244d-40ee-88af-7d2c7dc85c29", "machine_fingerprint": "f8fbfd8c9c34ec1d02ba14ba699f58fc3a149b107a20873d889920f97a24d0cc", "fingerprint_entropy": 4.453430532005257, "privacy_summary": {"critical_risks": 0, "high_risks": 0, "medium_risks": 169, "low_risks": 0, "raw_ids_detected": 126, "hashed_ids_detected": 0, "fingerprint_transmissions": 6}}