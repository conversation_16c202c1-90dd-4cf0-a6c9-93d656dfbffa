#!/usr/bin/env python3
"""
Simple Python script to test the telemetry detection patterns
This can be used to verify the patterns work before building the Rust version
"""

import re
import os
import sys
from pathlib import Path

# Telemetry and Machine ID patterns (simplified version of the Rust patterns)
PATTERNS = {
    "machine_id_generation": {
        "regex": r"(?i)(machine[_-]?id|device[_-]?id|hardware[_-]?id|system[_-]?id|unique[_-]?id)",
        "description": "Machine/Device ID generation or usage",
        "severity": "HIGH",
        "category": "MACHINE_ID"
    },
    "uuid_generation": {
        "regex": r"(?i)(uuid::v[1-5]|Uuid::new|uuid\.new|generate[_-]?uuid)",
        "description": "UUID generation for identification", 
        "severity": "MEDIUM",
        "category": "MACHINE_ID"
    },
    "mac_address": {
        "regex": r"(?i)(mac[_-]?address|get[_-]?mac|network[_-]?interface)",
        "description": "MAC address collection",
        "severity": "HIGH", 
        "category": "MACHINE_ID"
    },
    "telemetry_collection": {
        "regex": r"(?i)(telemetry|analytics|tracking|metrics|usage[_-]?data)",
        "description": "Telemetry/Analytics collection",
        "severity": "HIGH",
        "category": "TELEMETRY"
    },
    "data_transmission": {
        "regex": r"(?i)(send[_-]?data|transmit|upload[_-]?stats|report[_-]?usage)",
        "description": "Data transmission to external servers",
        "severity": "HIGH",
        "category": "TELEMETRY"
    },
    "rust_sysinfo": {
        "regex": r"(?i)(sysinfo::|System::new|get_processor|get_networks)",
        "description": "Rust sysinfo crate usage",
        "severity": "HIGH",
        "category": "RUST_SPECIFIC"
    },
    "rust_machine_uid": {
        "regex": r"(?i)(machine_uid|get_machine_id)",
        "description": "Rust machine-uid crate usage", 
        "severity": "HIGH",
        "category": "RUST_SPECIFIC"
    }
}

def scan_file(file_path):
    """Scan a single file for telemetry patterns"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
        detections = []
        lines = content.split('\n')
        
        for pattern_name, pattern_info in PATTERNS.items():
            regex = re.compile(pattern_info["regex"])
            
            for line_num, line in enumerate(lines, 1):
                if regex.search(line):
                    detections.append({
                        'file': file_path,
                        'line': line_num,
                        'content': line.strip(),
                        'pattern': pattern_info["description"],
                        'severity': pattern_info["severity"],
                        'category': pattern_info["category"]
                    })
        
        return detections
    except Exception as e:
        print(f"Error scanning {file_path}: {e}")
        return []

def scan_directory(directory, extensions=None):
    """Scan directory for files with telemetry patterns"""
    if extensions is None:
        extensions = ['.rs', '.py', '.js', '.ts', '.cpp', '.c', '.h', '.cs', '.java', '.go']
    
    all_detections = []
    files_scanned = 0
    
    print(f"🔍 Scanning directory: {directory}")
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            file_path = os.path.join(root, file)
            
            # Check file extension
            if any(file.lower().endswith(ext) for ext in extensions):
                files_scanned += 1
                detections = scan_file(file_path)
                all_detections.extend(detections)
                
                if files_scanned % 50 == 0:
                    print(f"📄 Scanned {files_scanned} files, {len(all_detections)} detections so far")
    
    return all_detections, files_scanned

def display_results(detections, files_scanned):
    """Display scan results"""
    print(f"\n📋 SCAN RESULTS")
    print(f"Files scanned: {files_scanned}")
    print(f"Detections found: {len(detections)}")
    
    if not detections:
        print("✅ No telemetry or machine ID patterns detected!")
        return
    
    print(f"\n🚨 DETECTIONS:")
    
    # Group by category
    by_category = {}
    for detection in detections:
        category = detection['category']
        if category not in by_category:
            by_category[category] = []
        by_category[category].append(detection)
    
    for category, cat_detections in by_category.items():
        print(f"\n{category} ({len(cat_detections)})")
        for detection in cat_detections[:5]:  # Show first 5 per category
            severity_symbol = "🔴" if detection['severity'] == 'HIGH' else "🟡" if detection['severity'] == 'MEDIUM' else "🟢"
            print(f"  {severity_symbol} {detection['severity']} {detection['file']}:{detection['line']}")
            print(f"    {detection['pattern']}")
            print(f"    {detection['content']}")
        
        if len(cat_detections) > 5:
            print(f"    ... and {len(cat_detections) - 5} more")

def main():
    if len(sys.argv) > 1:
        scan_path = sys.argv[1]
    else:
        # Test with the examples directory
        scan_path = "examples"
        if not os.path.exists(scan_path):
            scan_path = "."
    
    print("🔍 Telemetry & Machine ID Pattern Tester")
    print("=" * 50)
    
    if os.path.isfile(scan_path):
        # Scan single file
        detections = scan_file(scan_path)
        display_results(detections, 1)
    elif os.path.isdir(scan_path):
        # Scan directory
        detections, files_scanned = scan_directory(scan_path)
        display_results(detections, files_scanned)
    else:
        print(f"❌ Path not found: {scan_path}")
        print("Usage: python test_patterns.py [path]")
        sys.exit(1)

if __name__ == "__main__":
    main()
